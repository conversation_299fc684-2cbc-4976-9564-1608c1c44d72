package integration

import (
	"context"
	"database/sql"
	"errors"
	"fmt"
	"math/rand/v2"
	"time"

	"github.com/go-logr/logr"
	"github.com/prometheus/client_golang/prometheus"
	"github.com/prometheus/client_golang/prometheus/promauto"
	"github.com/volatiletech/sqlboiler/v4/queries/qm"
	corev1 "k8s.io/api/core/v1"
	v1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	runtimeutil "k8s.io/apimachinery/pkg/util/runtime"
	"k8s.io/apimachinery/pkg/util/wait"
	"k8s.io/client-go/informers"
	informerscorev1 "k8s.io/client-go/informers/core/v1"
	"k8s.io/client-go/kubernetes"
	listerscorev1 "k8s.io/client-go/listers/core/v1"
	"k8s.io/client-go/rest"
	"k8s.io/client-go/tools/cache"

	agentclient "github.com/akuityio/agent/pkg/client"
	"github.com/akuityio/agent/pkg/client/apis/clusteragent"
	"github.com/akuityio/agent/pkg/client/apis/clusterupgrader"
	"github.com/akuityio/agent/pkg/client/apis/controlplane"
	kargoagent "github.com/akuityio/agent/pkg/client/apis/kargo/clusteragent"
	kargoControlplane "github.com/akuityio/agent/pkg/client/apis/kargo/controlplane"
	"github.com/akuityio/agent/pkg/kube"
	"github.com/akuityio/akuity-platform/controllers/platform/integration/autoscaler"
	"github.com/akuityio/akuity-platform/controllers/platform/integration/clusterautoscaler"
	"github.com/akuityio/akuity-platform/controllers/platform/metrics"
	"github.com/akuityio/akuity-platform/controllers/shared/lib"
	"github.com/akuityio/akuity-platform/controllers/shared/tenant"
	"github.com/akuityio/akuity-platform/internal/argoproj"
	"github.com/akuityio/akuity-platform/internal/config"
	"github.com/akuityio/akuity-platform/internal/services/features"
	"github.com/akuityio/akuity-platform/internal/utils/database"
	"github.com/akuityio/akuity-platform/internal/utils/misc"
	"github.com/akuityio/akuity-platform/internal/version"
	"github.com/akuityio/akuity-platform/models/client"
	"github.com/akuityio/akuity-platform/models/events"
	"github.com/akuityio/akuity-platform/models/models"
)

const (
	instanceIDLabel  = "akuity.io/argo-cd-instance-id"
	clusterNameLabel = "akuity.io/argo-cd-cluster-name"

	kargoInstanceIDLabel = "akuity.io/kargo-instance-id"

	customAgentCertCmName = "agent-certs"
)

type platformController struct {
	informers                    []cache.SharedIndexInformer
	instancesController          database.TableController
	clustersController           database.TableController
	kargoInstancesController     database.TableController
	kargoAgentsController        database.TableController
	kargoEventsController        database.TableController
	eventsController             database.TableController
	aiConversationController     database.TableController
	aiConversationStepController database.TableController
	timelineEventsController     database.TableController
	watchers                     []lib.CanStart
	log                          *logr.Logger
	settings                     ControllerSettings
	featureSvc                   features.Service
}

type ControllerSettings struct {
	InstanceSubDomains             bool
	EnableIngress                  bool
	OverrideDefaultDomains         bool
	K8SClientSet                   kubernetes.Interface
	K8SRestConfig                  *rest.Config
	RepoSet                        client.RepoSet
	PortalDBRawClient              *sql.DB
	K3sDBRawClient                 *sql.DB
	K3sDBConnection                string
	K3sReadonlyHostname            string
	SharedK3sDBConnectionAuth      bool
	PortalDBConnection             string
	Log                            *logr.Logger
	InstanceConfig                 config.InstanceConfig
	IngressConfig                  config.IngressConfig
	ExternalDNS                    *controlplane.DataValuesIngressExternalDns
	DomainSuffix                   string
	Shard                          string
	PortalIPs                      []string
	ClusterIssuerName              string
	PortalURL                      string
	MultiClusterK8sDashboardConfig MultiClusterK8sDashboardConfig
	AIConfig                       config.AIConfig
}

func (s *ControllerSettings) GetDomainSeparator() string {
	if s.InstanceSubDomains {
		return "."
	}
	return "-"
}

type MultiClusterK8sDashboardConfig struct {
	SkipTLSVerify              bool
	ClusterInfoSyncPeriod      time.Duration
	ResourcesSyncPeriod        time.Duration
	EventSyncPeriod            time.Duration
	GarbageCollectionPeriod    time.Duration
	GarbageCollectionThreshold time.Duration
}

type TenantsFactory interface {
	NewTenant(instanceID string) (Tenant, error)
}

// Tenant interface encapsulates the tenant resources management logic.
type Tenant interface {
	Apply(ctx context.Context, cplaneValues *controlplane.DataValues, applyOpts kube.ApplyOpts) error
	InitializeTenant(ctx context.Context, cplaneValues *controlplane.DataValues, applyOpts kube.ApplyOpts, enableK3sCertCNReset bool) error
	ApplyCluster(ctx context.Context, clusterConfig agentclient.ClusterConfig) error
	InitializeClusterData(ctx context.Context, config agentclient.ClusterConfig, agentValues *clusteragent.DataValues, upgraderValues *clusterupgrader.DataValues) error
	Status(ctx context.Context, declarativeManagementEnabled, appsetDisabled bool) (*agentclient.AggregatedHealthResponse, error)
	CertificateStatus(ctx context.Context) (*agentclient.CertificateStatus, error)
	Delete(ctx context.Context) error
	DeleteCluster(context.Context, string, string) []error
	PruneTenant(ctx context.Context, generation uint64, dryRun bool) error
	PruneCluster(ctx context.Context, clusterName string, generation uint64, dryRun bool) error
	GetClusterAgentDataValues(context.Context, string, string, bool, bool) (*clusteragent.DataValues, error)
	DeleteClusterCredentials(ctx context.Context, name, oldSuffix, currentSuffix string, privileged bool) (bool, []error)
	ControlPlaneExternalKubeConfig(ctx context.Context, fqdn string, inSecure bool) ([]byte, error)
	ApplyDirectCluster(ctx context.Context, config agentclient.DirectClusterConfig) error
	DeleteDirectCluster(ctx context.Context, name string) []error
}

type KargoTenantsFactory interface {
	NewKargoTenant(instanceID string) (KargoTenant, error)
}

// KargoTenant interface encapsulates the tenant resources management logic.
type KargoTenant interface {
	Apply(ctx context.Context, cplaneValues *kargoControlplane.DataValues, applyOpts kube.ApplyOpts) error
	InitializeTenant(ctx context.Context, cplaneValues *kargoControlplane.DataValues, applyOpts kube.ApplyOpts, enableK3sCertCNReset bool) error
	ApplyAgent(ctx context.Context, clusterConfig agentclient.KargoAgentConfig, argocdConfig *rest.Config) error
	InitializeAgentData(ctx context.Context, config agentclient.KargoAgentConfig, agentValues *kargoagent.DataValues, upgraderValues *clusterupgrader.DataValues) error
	Status(ctx context.Context) (*agentclient.AggregatedHealthResponse, error)
	Delete(ctx context.Context) error
	DeleteAgent(context.Context, string, *rest.Config, bool) []error
	PruneTenant(ctx context.Context, generation uint64, dryRun bool) error
	PruneAgent(ctx context.Context, agentName string, generation uint64, dryRun bool) error
	GetAgentDataValues(ctx context.Context, name, suffix string, argocdRestConfig *rest.Config, inClusterK3sService bool) (*kargoagent.DataValues, error)
	DeleteAgentCredentials(ctx context.Context, name, currentSuffix string, argocdRestConfig *rest.Config) (bool, []error)
	AkuityManagedAgentStatus(ctx context.Context, agentName string) (*agentclient.AggregatedHealthResponse, error)
	GetGitOpsKubeConfig(ctx context.Context, fqdn string, inCluster bool) ([]byte, error)
	CertificateStatus(ctx context.Context) (*agentclient.CertificateStatus, error)
	AKPAdminServiceAccountToken(ctx context.Context) string
	UpdateAgentStatusCM(ctx context.Context, agentName string, status *agentclient.AgentStatusData) error
}

func (s *ControllerSettings) GetPortalDBContext() database.DBContext {
	return database.NewPGDbContext(s.PortalDBRawClient, s.PortalDBConnection)
}

func (s *ControllerSettings) GetK3SDBContext() database.DBContext {
	return database.NewPGDbContext(s.K3sDBRawClient, s.K3sDBConnection)
}

func (s *ControllerSettings) NewTenant(instanceID string) (Tenant, error) {
	return agentclient.NewArgoCDTenant(s.K8SRestConfig, *s.Log, instanceID)
}

func (s *ControllerSettings) NewKargoTenant(instanceID string) (KargoTenant, error) {
	return agentclient.NewKargoTenant(s.K8SRestConfig, *s.Log, instanceID)
}

func NewPlatformController(
	ctx context.Context,
	log *logr.Logger,
	settings ControllerSettings,
	featureSvc features.Service,
	cfg config.PlatformControllerConfig,
	metricsRegistry *metrics.ControllerMetricsRegistry,
) (*platformController, error) {
	stateClient := tenant.NewK3SStateClient(settings.K3sDBRawClient)

	kargoStateClient := tenant.NewKargoStateClient(settings.K3sDBRawClient)

	instanceFactory := informers.NewSharedInformerFactoryWithOptions(settings.K8SClientSet, cfg.ResyncDuration, informers.WithTweakListOptions(func(options *v1.ListOptions) {
		// watch resources with the instance ID label but not the cluster name label (to exclude agent in the same cluster)
		options.LabelSelector = fmt.Sprintf("%s,!%s", instanceIDLabel, clusterNameLabel)
	}))

	kargoInstanceFactory := informers.NewSharedInformerFactoryWithOptions(settings.K8SClientSet, cfg.ResyncDuration, informers.WithTweakListOptions(func(options *v1.ListOptions) {
		// watch resources with the kargo instance ID label
		// WARN: but do not remove cluster name label as akuity managed clusters need to be watched too
		options.LabelSelector = kargoInstanceIDLabel
	}))
	var ctrlInformers []cache.SharedIndexInformer
	instanceDeploymentsInformer := instanceFactory.Apps().V1().Deployments()
	ctrlInformers = append(ctrlInformers, instanceDeploymentsInformer.Informer())
	instanceStsInformer := instanceFactory.Apps().V1().StatefulSets()
	ctrlInformers = append(ctrlInformers, instanceStsInformer.Informer())
	kargoInstanceDeploymentsInformer := kargoInstanceFactory.Apps().V1().Deployments()
	ctrlInformers = append(ctrlInformers, kargoInstanceDeploymentsInformer.Informer())
	kargoInstanceStsInformer := kargoInstanceFactory.Apps().V1().StatefulSets()
	ctrlInformers = append(ctrlInformers, kargoInstanceStsInformer.Informer())
	pullSecretsGetter := func() (map[string][]byte, error) {
		return nil, nil
	}
	if cfg.ImagePullSecret != "" {
		pullSecretInformer := informerscorev1.NewFilteredSecretInformer(settings.K8SClientSet, cfg.GetNamespace(), cfg.ResyncDuration, cache.Indexers{}, func(options *v1.ListOptions) {
			options.FieldSelector = fmt.Sprintf("metadata.name=%s", cfg.ImagePullSecret)
		})
		ctrlInformers = append(ctrlInformers, pullSecretInformer)
		pullSecretsGetter = func() (map[string][]byte, error) {
			secret, err := listerscorev1.NewSecretLister(pullSecretInformer.GetIndexer()).Secrets(cfg.GetNamespace()).Get(cfg.ImagePullSecret)
			if err != nil {
				return nil, fmt.Errorf("image pull secret '%s' is configured by missing in namespace '%s': %w", cfg.ImagePullSecret, cfg.GetNamespace(), err)
			}
			return secret.Data, nil
		}
	}

	var scaler *autoscaler.ArgocdInstanceAutoscaler
	if featureSvc.GetFeatureStatuses(ctx, nil).GetAutoscaler().Enabled() {
		var err error
		scaler, err = autoscaler.NewArgoCDInstanceAutoscaler(log, settings.K8SClientSet, settings.RepoSet)
		if err != nil {
			return nil, fmt.Errorf("failed to construct an autoscaler: %w", err)
		}
	}

	var clusterScaler clusterautoscaler.Autoscaler
	if featureSvc.GetFeatureStatuses(ctx, nil).GetClusterAutoscaler().Enabled() {
		autoscalerConfig, err := clusterautoscaler.NewConfig(log, settings.K8SClientSet)
		if err != nil {
			return nil, fmt.Errorf("failed to construct a cluster autoscaler config: %w", err)
		}

		clusterScaler, err = clusterautoscaler.NewAutoscaler(log, autoscalerConfig, settings.K8SClientSet, settings.RepoSet)
		if err != nil {
			return nil, fmt.Errorf("failed to construct a cluster autoscaler: %w", err)
		}
	}

	customCerts, err := GetCustomCerts(context.Background(), settings.Log, settings.K8SClientSet, cfg.ControllerNamespace, customAgentCertCmName)
	if err != nil {
		return nil, fmt.Errorf("failed to get custom certs: %w", err)
	}
	kargoCert, argocdCert := customCerts.CommonCert, customCerts.CommonCert
	if customCerts.ArgocdCert != "" {
		argocdCert = customCerts.ArgocdCert
	}
	if customCerts.KargoCert != "" {
		kargoCert = customCerts.KargoCert
	}

	argocdVersions, err := misc.GetArgoCDVersions(*log)
	if err != nil {
		return nil, fmt.Errorf("failed to list the ArgoCD versions: %w", err)
	}

	kargoVersions, unstableVersion, err := misc.GetKargoVersions(*log)
	if err != nil {
		return nil, fmt.Errorf("failed to list the Kargo versions: %w", err)
	}

	argoCDInstanceReconciler := NewArgoCDInstanceReconciler(log, &settings, settings, stateClient, pullSecretsGetter, cfg.GracePeriodDuration, instanceDeploymentsInformer.Lister(), scaler, featureSvc, argocdVersions)
	argoCDEventsReconciler := NewArgoCDInstanceEventsReconciler(metricsRegistry, stateClient, settings)
	argoCDClusterReconciler := NewArgoCDClusterReconciler(&settings, settings, stateClient, featureSvc, clusterScaler, argocdCert, argocdVersions)

	kargoInstanceRec := NewKargoInstanceReconciler(&settings, settings, convertExternalDNS(settings.ExternalDNS), kargoStateClient, pullSecretsGetter, cfg.GracePeriodDuration, kargoInstanceDeploymentsInformer, featureSvc, kargoVersions, unstableVersion)
	kargoAgentRec := NewKargoAgentReconciler(&settings, settings, kargoStateClient, kargoCert, featureSvc, kargoVersions, unstableVersion)
	kargoEventsReconciler := NewKargoInstanceEventsReconciler(metricsRegistry, kargoStateClient, settings)
	aiConversationReconciler := NewAiConversationReconciler(featureSvc, settings.RepoSet, settings.PortalDBRawClient, settings.AIConfig, settings.K8SRestConfig)
	aiConversationStepReconciler := NewAiConversationStepReconciler(featureSvc, settings.RepoSet, settings.PortalDBRawClient, settings.AIConfig, settings.K8SRestConfig)
	timelineEventsReconciler := NewTimelineEventsReconciler(featureSvc, settings.RepoSet, tenant.NewK3SStateClient(settings.K3sDBRawClient), settings.PortalDBRawClient, cfg.AIConfig, settings.K8SRestConfig, *log)

	var instancesWatcher database.Watcher[events.Event] = database.NewWatcher[events.Event](*log, settings.GetPortalDBContext(), events.ArgoCDInstanceChannel)
	var instancesEventsWatcher database.Watcher[tenant.InstanceEvent] = database.NewWatcher[tenant.InstanceEvent](*log, settings.GetK3SDBContext(), tenant.InstanceEventsChannel)
	var clustersWatcher database.Watcher[events.Event] = database.NewWatcher[events.Event](*log, settings.GetPortalDBContext(), events.ArgoCDClusterChannel)

	var kargoInstancesEventsWatcher database.Watcher[tenant.InstanceEvent] = database.NewWatcher[tenant.InstanceEvent](*log, settings.GetK3SDBContext(), tenant.KargoInstanceEventsChannel)
	var kargoInstancesWatcher database.Watcher[events.Event] = database.NewWatcher[events.Event](*log, settings.GetPortalDBContext(), events.KargoInstanceChannel)
	var kargoAgentsWatcher database.Watcher[events.Event] = database.NewWatcher[events.Event](*log, settings.GetPortalDBContext(), events.KargoAgentChannel)
	var aiConversationWatcher database.Watcher[events.Event] = database.NewWatcher[events.Event](*log, settings.GetPortalDBContext(), events.AIConversationChannel)
	var timelineEventsWatcher database.Watcher[events.Event] = database.NewWatcher[events.Event](*log, settings.GetPortalDBContext(), events.TimelineEventsChannel)

	reconcileErrors := promauto.NewCounterVec(
		prometheus.CounterOpts{
			Name: "reconcile_errors",
			Help: "Total number of reconcile errors, per controller",
		}, []string{"controller"})

	enqueueHeartbeat := promauto.NewHistogramVec(prometheus.HistogramOpts{
		Name:    "enqueue_loop_duration_seconds",
		Help:    "Enqueue latencies distribution, per controller",
		Buckets: prometheus.LinearBuckets(0.01, 0.05, 40),
	}, []string{"controller"})

	reconcileHeartbeat := promauto.NewHistogramVec(prometheus.HistogramOpts{
		Name:    "reconcile_loop_duration_seconds",
		Help:    "Reconcile latencies distribution, per controller",
		Buckets: prometheus.LinearBuckets(0.01, 0.05, 40),
	}, []string{"controller"})

	pc := &platformController{
		informers:  ctrlInformers,
		featureSvc: featureSvc,
		watchers:   []lib.CanStart{instancesWatcher, clustersWatcher, instancesEventsWatcher, kargoInstancesWatcher, kargoAgentsWatcher, kargoInstancesEventsWatcher, aiConversationWatcher, timelineEventsWatcher},
		instancesController: database.NewTableController[*models.ArgoCDInstance](
			settings.RepoSet.ArgoCDInstances().Filter(models.ArgoCDInstanceWhere.Shard.EQ(settings.Shard)),
			*log,
			"argocd_instance",
			func(ctx context.Context) <-chan string {
				return lib.SubscribeToIds(ctx, instancesWatcher, func(e events.Event) bool {
					return e.Shard == settings.Shard && lib.EventSpecChanged(e)
				}, lib.GetEventId)
			},
			argoCDInstanceReconciler,
			reconcileErrors,
			enqueueHeartbeat,
			reconcileHeartbeat,
			database.WithResyncDuration[*models.ArgoCDInstance](cfg.ResyncDuration),
			database.WithReconciliationTimeout[*models.ArgoCDInstance](cfg.ReconciliationTimeout),
			database.WithEnqueueAllDelayFunc[*models.ArgoCDInstance](func() time.Duration {
				// Jitter before executing enqueueAll() operation, between 0 and 20% of ResyncDuration
				return time.Duration(float64(cfg.ResyncDuration) * 0.2 * rand.Float64())
			}),
			database.WithEnqueueDelayFunc[*models.ArgoCDInstance](func(i, count int) time.Duration {
				// Jitter when enqueueing an item into a queue, between 0 and 1000 ms
				return time.Duration(float64(1000)*rand.Float64()) * time.Millisecond
			}),
		),
		eventsController: database.NewTableController[*models.ArgoCDInstance](
			settings.RepoSet.ArgoCDInstances().Filter(models.ArgoCDInstanceWhere.Shard.EQ(settings.Shard)),

			*log,
			"argocd_instance_events",
			func(ctx context.Context) <-chan string {
				return lib.SubscribeToIds(ctx, instancesEventsWatcher, func(e tenant.InstanceEvent) bool {
					return e.Type == tenant.InstanceEventTypeK8SEventCreated
				}, func(e tenant.InstanceEvent) string {
					return e.InstanceID
				})
			},
			argoCDEventsReconciler,
			reconcileErrors,
			enqueueHeartbeat,
			reconcileHeartbeat,
			database.WithResyncDuration[*models.ArgoCDInstance](cfg.ResyncDuration),
			database.WithReconciliationTimeout[*models.ArgoCDInstance](cfg.ReconciliationTimeout),
			database.WithEnqueueAllDelayFunc[*models.ArgoCDInstance](func() time.Duration {
				// Jitter before executing enqueueAll() operation, between 0 and 20% of ResyncDuration
				return time.Duration(float64(cfg.ResyncDuration) * 0.2 * rand.Float64())
			}),
			database.WithEnqueueDelayFunc[*models.ArgoCDInstance](func(i, count int) time.Duration {
				// Jitter when enqueueing an item into a queue, between 0 and 1000 ms
				return time.Duration(float64(1000)*rand.Float64()) * time.Millisecond
			}),
		),
		kargoEventsController: database.NewTableController[*models.KargoInstance](
			settings.RepoSet.KargoInstances().Filter(models.KargoInstanceWhere.Shard.EQ(settings.Shard)),

			*log,
			"kargo_instance_events",
			func(ctx context.Context) <-chan string {
				return lib.SubscribeToIds(ctx, kargoInstancesEventsWatcher, func(e tenant.InstanceEvent) bool {
					return e.Type == tenant.InstanceEventTypeK8SEventCreated
				}, func(e tenant.InstanceEvent) string {
					return e.InstanceID
				})
			},
			kargoEventsReconciler,
			reconcileErrors,
			enqueueHeartbeat,
			reconcileHeartbeat,
			database.WithResyncDuration[*models.KargoInstance](cfg.ResyncDuration),
			database.WithReconciliationTimeout[*models.KargoInstance](cfg.ReconciliationTimeout),
			database.WithEnqueueAllDelayFunc[*models.KargoInstance](func() time.Duration {
				// Jitter before executing enqueueAll() operation, between 0 and 20% of ResyncDuration
				return time.Duration(float64(cfg.ResyncDuration) * 0.2 * rand.Float64())
			}),
			database.WithEnqueueDelayFunc[*models.KargoInstance](func(i, count int) time.Duration {
				// Jitter when enqueueing an item into a queue, between 0 and 1000 ms
				return time.Duration(float64(1000)*rand.Float64()) * time.Millisecond
			}),
		),
		clustersController: database.NewTableController[*models.ArgoCDCluster](
			settings.RepoSet.ArgoCDClusters().Filter(
				qm.Load(models.ArgoCDClusterRels.Instance),
				qm.InnerJoin("argo_cd_instance on argo_cd_instance.id = argo_cd_cluster.instance_id"),
				models.ArgoCDInstanceWhere.Shard.EQ(settings.Shard),
			),
			*log,
			"argocd_clusters",
			func(ctx context.Context) <-chan string {
				res := make(chan string)
				clusterEvents := clustersWatcher.Subscribe(ctx, func(e events.Event) bool {
					return e.Shard == settings.Shard && lib.EventSpecChanged(e)
				})
				clusterChangedEvents := instancesEventsWatcher.Subscribe(ctx, func(e tenant.InstanceEvent) bool {
					return e.Type == tenant.InstanceEventTypeClusterStatusChanged
				})
				go func() {
					for {
						select {
						case <-ctx.Done():
							return
						case event := <-clusterEvents:
							res <- event.ID
						case event := <-clusterChangedEvents:
							cluster, err := settings.RepoSet.ArgoCDClusters(qm.Select(fmt.Sprintf("%s as %s", models.ArgoCDClusterTableColumns.ID, models.ArgoCDClusterColumns.ID))).Filter(
								qm.Load(models.ArgoCDClusterRels.Instance),
								qm.InnerJoin("argo_cd_instance on argo_cd_instance.id = argo_cd_cluster.instance_id"),
								models.ArgoCDInstanceWhere.Shard.EQ(settings.Shard),
								models.ArgoCDClusterWhere.InstanceID.EQ(event.InstanceID),
								models.ArgoCDClusterWhere.Name.EQ(event.ClusterName),
							).One(ctx)
							if err == nil {
								res <- cluster.ID
							} else if !errors.Is(err, sql.ErrNoRows) {
								log.Error(err, "failed to extract cluster id from cluster event", "cluster", event.ClusterName, "instance", event.InstanceID)
							}
						}
					}
				}()
				return res
			},
			argoCDClusterReconciler,
			reconcileErrors,
			enqueueHeartbeat,
			reconcileHeartbeat,
			database.WithResyncDuration[*models.ArgoCDCluster](cfg.ResyncDuration),
			database.WithReconciliationTimeout[*models.ArgoCDCluster](cfg.ReconciliationTimeout),
			database.WithEnqueueAllDelayFunc[*models.ArgoCDCluster](func() time.Duration {
				// Jitter before executing enqueueAll() operation, between 0 and 20% of ResyncDuration
				return time.Duration(float64(cfg.ResyncDuration) * 0.2 * rand.Float64())
			}),
			database.WithEnqueueDelayFunc[*models.ArgoCDCluster](func(i, count int) time.Duration {
				// Jitter when enqueueing an item into a queue, between 0 and 1000 ms
				return time.Duration(float64(1000)*rand.Float64()) * time.Millisecond
			}),
		),
		kargoInstancesController: database.NewTableController[*models.KargoInstance](
			settings.RepoSet.KargoInstances().Filter(models.KargoInstanceWhere.Shard.EQ(settings.Shard)),
			*log,
			"kargo_instance",
			func(ctx context.Context) <-chan string {
				return lib.SubscribeToIds(ctx, kargoInstancesWatcher, func(e events.Event) bool {
					return e.Shard == settings.Shard && lib.EventSpecChanged(e)
				}, lib.GetEventId)
			},
			kargoInstanceRec,
			reconcileErrors,
			enqueueHeartbeat,
			reconcileHeartbeat,
			database.WithResyncDuration[*models.KargoInstance](cfg.ResyncDuration),
			database.WithReconciliationTimeout[*models.KargoInstance](cfg.ReconciliationTimeout),
			database.WithEnqueueAllDelayFunc[*models.KargoInstance](func() time.Duration {
				// Jitter before executing enqueueAll() operation, between 0 and 20% of ResyncDuration
				return time.Duration(float64(cfg.ResyncDuration) * 0.2 * rand.Float64())
			}),
			database.WithEnqueueDelayFunc[*models.KargoInstance](func(i, count int) time.Duration {
				// Jitter when enqueueing an item into a queue, between 0 and 1000 ms
				return time.Duration(float64(1000)*rand.Float64()) * time.Millisecond
			}),
		),
		kargoAgentsController: database.NewTableController[*models.KargoAgent](
			settings.RepoSet.KargoAgents().Filter(
				qm.InnerJoin("kargo_instance on kargo_instance.id = kargo_agent.instance_id"),
				models.KargoInstanceWhere.Shard.EQ(settings.Shard),
			),
			*log,
			"kargo_agents",
			func(ctx context.Context) <-chan string {
				res := make(chan string)
				clusterEvents := kargoAgentsWatcher.Subscribe(ctx, func(e events.Event) bool {
					return e.Shard == settings.Shard && lib.EventSpecChanged(e)
				})
				clusterChangedEvents := kargoInstancesEventsWatcher.Subscribe(ctx, func(e tenant.InstanceEvent) bool {
					return e.Type == tenant.InstanceEventTypeClusterStatusChanged
				})

				go func() {
					for {
						select {
						case <-ctx.Done():
							return
						case event := <-clusterEvents:
							res <- event.ID
						case event := <-clusterChangedEvents:
							cluster, err := settings.RepoSet.KargoAgents(qm.Select(fmt.Sprintf("%s as %s", models.KargoAgentTableColumns.ID, models.KargoAgentColumns.ID))).Filter(
								qm.InnerJoin("kargo_instance on kargo_instance.id = kargo_agent.instance_id"),
								models.KargoInstanceWhere.Shard.EQ(settings.Shard),
								models.KargoAgentWhere.InstanceID.EQ(event.InstanceID),
								models.KargoAgentWhere.Name.EQ(event.ClusterName),
							).One(ctx)
							if err == nil {
								res <- cluster.ID
							} else if !errors.Is(err, sql.ErrNoRows) {
								log.Error(err, "failed to extract kargo cluster id from cluster event", "cluster", event.ClusterName, "instance", event.InstanceID)
							}
						}
					}
				}()
				return res
			},
			kargoAgentRec,
			reconcileErrors,
			enqueueHeartbeat,
			reconcileHeartbeat,
			database.WithResyncDuration[*models.KargoAgent](cfg.ResyncDuration),
			database.WithReconciliationTimeout[*models.KargoAgent](cfg.ReconciliationTimeout),
			database.WithEnqueueAllDelayFunc[*models.KargoAgent](func() time.Duration {
				// Jitter before executing enqueueAll() operation, between 0 and 20% of ResyncDuration
				return time.Duration(float64(cfg.ResyncDuration) * 0.2 * rand.Float64())
			}),
			database.WithEnqueueDelayFunc[*models.KargoAgent](func(i, count int) time.Duration {
				// Jitter when enqueueing an item into a queue, between 0 and 1000 ms
				return time.Duration(float64(1000)*rand.Float64()) * time.Millisecond
			}),
		),
		aiConversationController: database.NewTableController[*models.AiConversation](
			settings.RepoSet.AIConversations().Filter(
				qm.Load(models.AiConversationRels.Instance),
				qm.LeftOuterJoin("argo_cd_instance on argo_cd_instance.id = ai_conversation.instance_id"),
				qm.Where("coalesce(argo_cd_instance.shard, '') = ?", settings.Shard),
				qm.Where("exists (select 1 from jsonb_array_elements(messages) as m where coalesce ((m->>'processed')::bool, false) = false) or (metadata->'retry'->>'after')::timestamp > now()"),
			),
			*log,
			"ai_conversation",
			func(ctx context.Context) <-chan string {
				return lib.SubscribeToIds(ctx, aiConversationWatcher, func(e events.Event) bool {
					return e.Shard == settings.Shard
				}, func(e events.Event) string {
					return e.ID
				})
			},
			aiConversationReconciler,
			reconcileErrors,
			enqueueHeartbeat,
			reconcileHeartbeat,
			database.WithResyncDuration[*models.AiConversation](cfg.ResyncDuration),
			database.WithReconciliationTimeout[*models.AiConversation](cfg.ReconciliationTimeout),
			database.WithRequeueBackoff[*models.AiConversation](wait.Backoff{
				Duration: 5 * time.Second,
				Factor:   2.0,
				Cap:      15 * time.Minute,
				Steps:    9999,
			}),
			database.WithEnqueueAllDelayFunc[*models.AiConversation](func() time.Duration {
				// Jitter before executing enqueueAll() operation, between 0 and 20% of ResyncDuration
				return time.Duration(float64(cfg.ResyncDuration) * 0.2 * rand.Float64())
			}),
			database.WithEnqueueDelayFunc[*models.AiConversation](func(i, count int) time.Duration {
				// Jitter when enqueueing an item into a queue, between 0 and 1000 ms
				return time.Duration(float64(1000)*rand.Float64()) * time.Millisecond
			}),
		),
		aiConversationStepController: database.NewTableController[*models.AiConversation](
			settings.RepoSet.AIConversations().Filter(
				qm.Load(models.AiConversationRels.Instance),
				qm.LeftOuterJoin("argo_cd_instance on argo_cd_instance.id = ai_conversation.instance_id"),
				qm.Where("coalesce(argo_cd_instance.shard, '') = ?", settings.Shard),
				// Filter for conversations that have at least one unprocessed tool call
				qm.Where(`exists (
					select 1
					from jsonb_array_elements(ai_conversation.messages) as m
					where m -> 'message' ->> 'role' = 'tool' 
					and m ->> 'toolCallSummary' is null
				)`),
			),
			*log,
			"ai_conversation_step",
			func(ctx context.Context) <-chan string {
				return lib.SubscribeToIds(ctx, aiConversationWatcher, func(e events.Event) bool {
					return e.Shard == settings.Shard
				}, func(e events.Event) string {
					return e.ID
				})
			},
			aiConversationStepReconciler,
			reconcileErrors,
			enqueueHeartbeat,
			reconcileHeartbeat,
			database.WithResyncDuration[*models.AiConversation](cfg.ResyncDuration),
			database.WithReconciliationTimeout[*models.AiConversation](cfg.ReconciliationTimeout),
			database.WithEnqueueAllDelayFunc[*models.AiConversation](func() time.Duration {
				// Jitter before executing enqueueAll() operation, between 0 and 20% of ResyncDuration
				return time.Duration(float64(cfg.ResyncDuration) * 0.2 * rand.Float64())
			}),
			database.WithEnqueueDelayFunc[*models.AiConversation](func(i, count int) time.Duration {
				// Jitter when enqueueing an item into a queue, between 0 and 1000 ms
				return time.Duration(float64(1000)*rand.Float64()) * time.Millisecond
			}),
		),
		timelineEventsController: database.NewTableController[*models.ArgoCDInstance](
			settings.RepoSet.ArgoCDInstances().Filter(models.ArgoCDInstanceWhere.Shard.EQ(settings.Shard)),
			*log,
			"timeline_events",
			func(ctx context.Context) <-chan string {
				res := make(chan string)
				instanceEvents := lib.SubscribeToIds(ctx, timelineEventsWatcher, func(e events.Event) bool {
					return e.Shard == settings.Shard
				}, lib.GetEventId)
				instanceChanges := lib.SubscribeToIds(ctx, instancesWatcher, func(e events.Event) bool {
					return e.Shard == settings.Shard
				}, lib.GetEventId)

				go func() {
					for {
						select {
						case <-ctx.Done():
							return
						case id := <-instanceEvents:
							res <- id
						case id := <-instanceChanges:
							res <- id
						}
					}
				}()

				return res
			},
			timelineEventsReconciler,
			reconcileErrors,
			enqueueHeartbeat,
			reconcileHeartbeat,
			database.WithResyncDuration[*models.ArgoCDInstance](cfg.ResyncDuration),
			database.WithReconciliationTimeout[*models.ArgoCDInstance](cfg.ReconciliationTimeout),
			database.WithEnqueueAllDelayFunc[*models.ArgoCDInstance](func() time.Duration {
				// Jitter before executing enqueueAll() operation, between 0 and 20% of ResyncDuration
				return time.Duration(float64(cfg.ResyncDuration) * 0.2 * rand.Float64())
			}),
			database.WithEnqueueDelayFunc[*models.ArgoCDInstance](func(i, count int) time.Duration {
				// Jitter when enqueueing an item into a queue, between 0 and 1000 ms
				return time.Duration(float64(1000)*rand.Float64()) * time.Millisecond
			}),
		),
		settings: settings,
		log:      log,
	}
	informerHandlers := cache.ResourceEventHandlerFuncs{
		AddFunc: pc.enqueueByInstanceLabel,
		UpdateFunc: func(old, new interface{}) {
			pc.enqueueByInstanceLabel(new)
		},
	}
	if _, err := instanceDeploymentsInformer.Informer().AddEventHandler(informerHandlers); err != nil {
		return nil, err
	}
	if _, err := instanceStsInformer.Informer().AddEventHandler(informerHandlers); err != nil {
		return nil, err
	}
	if _, err := kargoInstanceDeploymentsInformer.Informer().AddEventHandler(informerHandlers); err != nil {
		return nil, err
	}
	if _, err := kargoInstanceStsInformer.Informer().AddEventHandler(informerHandlers); err != nil {
		return nil, err
	}

	argoCDEventsReconciler.AddEventHandler(func(instanceID string, event corev1.Event) {
		// reconcile instance every time when operation started/completed to refresh counter of in-progress operations
		if event.InvolvedObject.Kind == argoproj.ApplicationKind && event.InvolvedObject.APIVersion == argoproj.APIVersion && (event.Reason == argoproj.EventReasonOperationStarted || event.Reason == argoproj.EventReasonOperationCompleted) {
			pc.instancesController.Enqueue(instanceID)
		}
	})

	return pc, nil
}

func (pc *platformController) Init(ctx context.Context) error {
	var cacheSyncs []cache.InformerSynced
	for _, item := range pc.informers {
		cacheSyncs = append(cacheSyncs, item.HasSynced)
		(func(informer cache.SharedIndexInformer) {
			go informer.Run(ctx.Done())
		})(item)
	}

	for _, watcher := range pc.watchers {
		if err := watcher.Start(ctx); err != nil {
			return err
		}
	}

	if !cache.WaitForCacheSync(ctx.Done(), cacheSyncs...) {
		return errors.New("timed out waiting for caches to sync")
	}
	return nil
}

func (pc *platformController) enqueueByInstanceLabel(obj interface{}) {
	hasLabels, ok := obj.(v1.ObjectMetaAccessor)
	if !ok {
		return
	}

	labels := hasLabels.GetObjectMeta().GetLabels()
	if labels == nil {
		return
	}
	argoCDInstanceID, ok := labels[instanceIDLabel]
	if ok {
		pc.instancesController.Enqueue(argoCDInstanceID)
		return
	}

	kargoInstanceID, ok := labels[kargoInstanceIDLabel]
	if ok {
		// check if deployment is an akuity managed cluster component,
		// if yes enqueue to cluster controller instead
		clusterID, ok := labels[kargoAgentIDLabel]
		if ok {
			pc.kargoAgentsController.Enqueue(clusterID)
			return
		}
		pc.kargoInstancesController.Enqueue(kargoInstanceID)
	}
}

func (pc *platformController) Run(ctx context.Context, numWorkers int) error {
	versionInfo := version.GetVersion()
	pc.log.Info("Akuity Platform Controller Starting", "version", versionInfo.Version, "build_date", versionInfo.BuildDate)

	defer runtimeutil.HandleCrash()

	if err := pc.instancesController.Start(ctx, numWorkers); err != nil {
		return err
	}

	if err := pc.clustersController.Start(ctx, numWorkers); err != nil {
		return err
	}

	if err := pc.eventsController.Start(ctx, numWorkers); err != nil {
		return err
	}

	if err := pc.kargoEventsController.Start(ctx, numWorkers); err != nil {
		return err
	}

	if err := pc.kargoInstancesController.Start(ctx, numWorkers); err != nil {
		return err
	}

	if err := pc.kargoAgentsController.Start(ctx, numWorkers); err != nil {
		return err
	}

	if err := pc.aiConversationController.Start(ctx, numWorkers); err != nil {
		return err
	}

	if err := pc.aiConversationStepController.Start(ctx, numWorkers); err != nil {
		return err
	}

	if err := pc.timelineEventsController.Start(ctx, numWorkers); err != nil {
		return err
	}

	<-ctx.Done()
	return nil
}

func convertExternalDNS(argoDNS *controlplane.DataValuesIngressExternalDns) *kargoControlplane.DataValuesIngressExternalDns {
	return &kargoControlplane.DataValuesIngressExternalDns{
		SetIdentifier: argoDNS.SetIdentifier,
		AwsFailover:   argoDNS.AwsFailover,
		HealthCheckId: argoDNS.HealthCheckId,
		Target:        argoDNS.Target,
	}
}
