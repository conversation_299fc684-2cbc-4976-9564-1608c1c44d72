#@ load("@ytt:data", "data")

kind: PersistentVolume
apiVersion: v1
metadata:
  name: postgres
  labels:
    type: local
    app: postgres
spec:
  storageClassName: #@ data.values.dev.storageClassName
  capacity:
    storage: 5Gi
  accessModes:
    - ReadWriteOnce
  hostPath:
    path: "/mnt/data"

---
kind: PersistentVolumeClaim
apiVersion: v1
metadata:
  name: postgres
  labels:
    app: postgres
spec:
  storageClassName: #@ data.values.dev.storageClassName
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 5Gi

---
kind: Deployment
apiVersion: apps/v1
metadata:
  name: postgres
spec:
  replicas: 1
  selector:
    matchLabels:
      app: postgres
  template:
    metadata:
      labels:
        app: postgres
    spec:
      containers:
        - name: postgres
          image: postgres:17.2
          ports:
            - containerPort: 5432
          envFrom:
            - configMapRef:
                name: postgres
          volumeMounts:
            - mountPath: /var/lib/postgresql/data
              name: postgres
          resources:
            requests:
              cpu: 1000m
              memory: 500Mi
      volumes:
        - name: postgres
          persistentVolumeClaim:
            claimName: postgres

---
kind: ConfigMap
apiVersion: v1
metadata:
  name: postgres
data:
  POSTGRES_DB: postgres
  POSTGRES_USER: postgres
  POSTGRES_PASSWORD: postgres

---
apiVersion: v1
kind: Service
metadata:
  name: postgres
  labels:
    dev-port-forward: "true"
spec:
  ports:
    - port: 5432
      targetPort: 5432
  selector:
    app: postgres
