package ai

import (
	"context"
	"strings"

	"github.com/sa<PERSON><PERSON><PERSON>/go-openai/jsonschema"
)

type settingsKeyType struct{}

var settingsKey = settingsKeyType{}

type Settings struct {
	Model string
}

func WithSettings(ctx context.Context, settings Settings) context.Context {
	return context.WithValue(ctx, settingsKey, settings)
}

func getSettings(ctx context.Context) Settings {
	if settings, ok := ctx.Value(settingsKey).(Settings); ok {
		return settings
	}
	return Settings{}
}

func GetModelVersion(provider string) string {
	switch provider {
	case ProviderAnthropic:
		model := string(AnthropicModelVersion)
		return model[:strings.LastIndex(model, "-")] // remove last '-' and keep the rest
	case ProviderOpenAI:
		return OpenAIModelVersion
	default:
		return OpenAIModelVersion
	}
}

var (
	ProviderOpenAI    = "openai"
	ProviderAnthropic = "anthropic"

	// MessageRoleUser is a message sent by the user.
	MessageRoleUser = "user"
	// MessageRoleAssistant is a message sent by the AI assistant.
	MessageRoleAssistant = "assistant"
	// MessageRoleSystem is a message sent by the system, providing context or instructions.
	MessageRoleSystem = "system"
	// MessageRoleDeveloper is a special kind of system message: provides an additional context or instructions from a developer.
	MessageRoleDeveloper = "developer"
	// MessageRoleTool is a message that represents a tool call result.
	MessageRoleTool = "tool"
)

// ToolCall represents a call to a tool, including its ID, name, and arguments.
type ToolCall struct {
	ID        string `json:"id"`
	Name      string `json:"name"`
	Arguments string `json:"arguments"`
}

// Tool represents a tool that can be called by the AI, including its name, description, and parameters schema.
type Tool struct {
	Name         string                `json:"name"`
	Description  string                `json:"description"`
	ParamsSchema jsonschema.Definition `json:"params_schema"`
}

// Message represents a message in the conversation, including its role, content, and optional tool calls.
type Message struct {
	Role       string     `json:"role"`
	Content    string     `json:"content"`
	ToolCalls  []ToolCall `json:"tool_calls,omitempty"`
	ToolCallID string     `json:"tool_call_id,omitempty"`
}

type ResponseFormat struct {
	JSONSchema *jsonschema.Definition `json:"json_schema,omitempty"`
}

type Request struct {
	Messages       []Message       `json:"messages"`
	Tools          []Tool          `json:"tools,omitempty"`
	ResponseFormat *ResponseFormat `json:"response_format,omitempty"`
}

type Choice struct {
	Message Message `json:"message"`
}

type Usage struct {
	InputTokens       int     `json:"input_tokens,omitempty"`
	CachedInputTokens int     `json:"cached_input_tokens,omitempty"`
	OutputTokens      int     `json:"output_tokens,omitempty"`
	Cost              float64 `json:"cost,omitempty"`
	InputTokenPrice   float64 `json:"input_token_price,omitempty"`
}

type Response struct {
	Choices []Choice `json:"choices"`
	Usage   Usage    `json:"usage,omitempty"`
	Model   string   `json:"model,omitempty"`
}

type Client interface {
	Message(ctx context.Context, req Request) (*Response, error)
}
