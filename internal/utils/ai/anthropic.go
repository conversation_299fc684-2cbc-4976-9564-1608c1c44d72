package ai

import (
	"context"
	"encoding/json"
	"errors"

	"github.com/anthropics/anthropic-sdk-go"
	"github.com/anthropics/anthropic-sdk-go/option"
	"github.com/anthropics/anthropic-sdk-go/shared/constant"
	"github.com/samber/lo"
)

const (
	AnthropicModelVersion = anthropic.ModelClaude4Sonnet20250514
	// Claude Web Search configuration
	AnthropicModelWithSearchSupport = "claude-sonnet-4-20250514"
	ClaudeWebSearchToolType         = "web_search_20250305"
	ClaudeWebSearchToolName         = "web_search"

	anthropicInputTokenPrice       = 3.0 / 1000000.0
	anthropicCachedInputTokenPrice = 0.3 / 1000000.0
	anthropicOutputTokenPrice      = 15.0 / 1000000.0
)

type anthropicClient struct {
	client anthropic.Client
}

func NewAnthropicClient(apiKey string) Client {
	return instrumentClient(ProviderAnthropic, &anthropicClient{
		client: anthropic.NewClient(option.WithAPIKey(apiKey)),
	})
}

func buildAnthropicMessageNewParams(model anthropic.Model, req Request) anthropic.MessageNewParams {
	var tools []anthropic.ToolUnionParam
	if model == anthropic.ModelClaudeSonnet4_20250514 {
		tools = append(tools, anthropic.ToolUnionParam{
			// for the details parametes can be visited at https://docs.anthropic.com/en/docs/agents-and-tools/tool-use/web-search-tool#how-to-use-web-search
			OfWebSearchTool20250305: &anthropic.WebSearchTool20250305Param{
				Type: ClaudeWebSearchToolType,
				Name: ClaudeWebSearchToolName,
			},
		})
	} else {
		tools = lo.Map(req.Tools, func(item Tool, _ int) anthropic.ToolUnionParam {
			return anthropic.ToolUnionParam{
				OfTool: &anthropic.ToolParam{
					Type:        anthropic.ToolTypeCustom,
					Name:        item.Name,
					Description: anthropic.String(item.Description),
					InputSchema: anthropic.ToolInputSchemaParam{
						Properties: item.ParamsSchema.Properties,
						Type:       constant.Object(item.ParamsSchema.Type),
					},
				},
			}
		})
	}

	return anthropic.MessageNewParams{
		MaxTokens: 20000,
		Model:     model,
		Tools:     tools,
	}
}

func (c *anthropicClient) Message(ctx context.Context, req Request) (*Response, error) {
	model := AnthropicModelVersion
	if settings := getSettings(ctx); settings.Model != "" {
		model = anthropic.Model(settings.Model)
	}

	params := buildAnthropicMessageNewParams(model, req)

	if req.ResponseFormat != nil && req.ResponseFormat.JSONSchema != nil {
		schemaJSON, err := req.ResponseFormat.JSONSchema.MarshalJSON()
		if err != nil {
			return nil, err
		}
		params.System = append(params.System, anthropic.TextBlockParam{
			Text: "The response should be in JSON format that matches the following schema: " + string(schemaJSON),
		})
	}
	for _, item := range req.Messages {
		switch item.Role {
		case MessageRoleSystem:
			if item.Content != "" {
				params.System = append(params.System, anthropic.TextBlockParam{Text: item.Content})
			}
		case MessageRoleDeveloper, MessageRoleUser:
			if item.Role == MessageRoleDeveloper {
				item.Content = "Developer (IMPORTANT): " + item.Content
			}
			params.Messages = append(params.Messages, anthropic.MessageParam{
				Role: anthropic.MessageParamRoleUser,
				Content: []anthropic.ContentBlockParamUnion{{
					OfText: &anthropic.TextBlockParam{Text: item.Content},
				}},
			})
		case MessageRoleAssistant:
			var blocks []anthropic.ContentBlockParamUnion
			if item.Content != "" {
				blocks = append(blocks, anthropic.NewTextBlock(item.Content))
			}
			for _, toolCall := range item.ToolCalls {
				args := map[string]interface{}{}
				if err := json.Unmarshal([]byte(toolCall.Arguments), &args); err != nil {
					return nil, err
				}
				blocks = append(blocks, anthropic.NewToolUseBlock(toolCall.ID, args, toolCall.Name))
			}
			params.Messages = append(params.Messages, anthropic.NewAssistantMessage(blocks...))
		case MessageRoleTool:
			params.Messages = append(params.Messages, anthropic.NewUserMessage(anthropic.NewToolResultBlock(item.ToolCallID, item.Content, false)))
		}
	}

	resp, err := c.client.Messages.New(ctx, params)
	if err != nil {
		var apierr *anthropic.Error
		if errors.As(err, &apierr) {
			return nil, NewAPIError(err, apierr.StatusCode)
		}
		return nil, err
	}
	msg := Message{Role: MessageRoleAssistant}
	for _, block := range resp.Content {
		switch block := block.AsAny().(type) {
		case anthropic.TextBlock:
			msg.Content = block.Text
		case anthropic.ToolUseBlock:
			inputJSON, err := json.Marshal(block.Input)
			if err != nil {
				return nil, err
			}
			msg.ToolCalls = append(msg.ToolCalls, ToolCall{
				ID:        block.ID,
				Name:      block.Name,
				Arguments: string(inputJSON),
			})
		}
	}

	return &Response{
		Usage: Usage{
			InputTokens:       int(resp.Usage.InputTokens),
			CachedInputTokens: int(resp.Usage.CacheReadInputTokens),
			OutputTokens:      int(resp.Usage.OutputTokens),
			Cost: anthropicInputTokenPrice*float64(resp.Usage.InputTokens) +
				anthropicCachedInputTokenPrice*float64(resp.Usage.CacheReadInputTokens) +
				anthropicOutputTokenPrice*float64(resp.Usage.OutputTokens),
			InputTokenPrice: anthropicInputTokenPrice,
		},
		Choices: []Choice{{Message: msg}},
		Model:   string(resp.Model),
	}, nil
}
