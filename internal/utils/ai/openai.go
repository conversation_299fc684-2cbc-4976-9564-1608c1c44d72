package ai

import (
	"context"
	"errors"

	"github.com/samber/lo"
	"github.com/sashabaranov/go-openai"
)

const (
	OpenAIModelVersion           = openai.GPT4Dot1
	OpenAIModelVersionWithSearch = "gpt-4o-mini-search-preview"

	openaiInputTokenPrice       = 2.0 / 1000000.0
	openAICachedInputTokenPrice = 0.5 / 1000000.0
	openAIOutputTokenPrice      = 8.0 / 1000000.0
)

type openAIClient struct {
	*openai.Client
}

func (c *openAIClient) Message(ctx context.Context, req Request) (*Response, error) {
	var respFormat *openai.ChatCompletionResponseFormat
	if req.ResponseFormat != nil && req.ResponseFormat.JSONSchema != nil {
		respFormat = &openai.ChatCompletionResponseFormat{
			Type: openai.ChatCompletionResponseFormatTypeJSONSchema,
			JSONSchema: &openai.ChatCompletionResponseFormatJSONSchema{
				Name:        "response",
				Description: "Response to the user",
				Strict:      true,
				Schema:      req.ResponseFormat.JSONSchema,
			},
		}
	}
	model := OpenAIModelVersion
	if settings := getSettings(ctx); settings.Model != "" {
		model = settings.Model
	}
	resp, err := c.CreateChatCompletion(ctx, openai.ChatCompletionRequest{
		Model: model,
		Messages: lo.Map(req.Messages, func(item Message, _ int) openai.ChatCompletionMessage {
			return openai.ChatCompletionMessage{
				Role:       item.Role,
				Content:    item.Content,
				ToolCallID: item.ToolCallID,
				ToolCalls: lo.Map(item.ToolCalls, func(toolCall ToolCall, _ int) openai.ToolCall {
					return openai.ToolCall{
						ID:   toolCall.ID,
						Type: openai.ToolTypeFunction,
						Function: openai.FunctionCall{
							Name:      toolCall.Name,
							Arguments: toolCall.Arguments,
						},
					}
				}),
			}
		}),
		ResponseFormat: respFormat,
		Tools: lo.Map(req.Tools, func(item Tool, _ int) openai.Tool {
			return openai.Tool{
				Type: openai.ToolTypeFunction,
				Function: &openai.FunctionDefinition{
					Name:        item.Name,
					Description: item.Description,
					Parameters:  item.ParamsSchema,
				},
			}
		}),
	})
	if err != nil {
		var openaiErr *openai.APIError
		if errors.As(err, &openaiErr) {
			return nil, NewAPIError(err, openaiErr.HTTPStatusCode)
		}
		return nil, err
	}

	return &Response{
		Usage: Usage{
			InputTokens:       resp.Usage.PromptTokens - resp.Usage.PromptTokensDetails.CachedTokens,
			CachedInputTokens: resp.Usage.PromptTokensDetails.CachedTokens,
			OutputTokens:      resp.Usage.CompletionTokens,
			Cost: openaiInputTokenPrice*float64(resp.Usage.PromptTokens-resp.Usage.PromptTokensDetails.CachedTokens) +
				openAICachedInputTokenPrice*float64(resp.Usage.PromptTokensDetails.CachedTokens) +
				openAIOutputTokenPrice*float64(resp.Usage.CompletionTokens),
			InputTokenPrice: openaiInputTokenPrice,
		},
		Model: resp.Model,
		Choices: lo.Map(resp.Choices, func(item openai.ChatCompletionChoice, _ int) Choice {
			return Choice{
				Message: Message{
					Role:       item.Message.Role,
					Content:    item.Message.Content,
					ToolCallID: item.Message.ToolCallID,
					ToolCalls: lo.Map(item.Message.ToolCalls, func(item openai.ToolCall, _ int) ToolCall {
						return ToolCall{ID: item.ID, Name: item.Function.Name, Arguments: item.Function.Arguments}
					}),
				},
			}
		}),
	}, nil
}

func NewOpenAIClient(apiKey string) Client {
	return instrumentClient(ProviderOpenAI, &openAIClient{openai.NewClient(apiKey)})
}
