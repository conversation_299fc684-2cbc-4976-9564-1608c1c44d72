package ai

import (
	"context"
	"errors"
	"strconv"
	"time"

	"github.com/prometheus/client_golang/prometheus"
	"github.com/prometheus/client_golang/prometheus/promauto"

	"github.com/akuityio/akuity-platform/internal/utils/logging"
)

var (
	apiCallsTotal = promauto.NewCounterVec(
		prometheus.CounterOpts{
			Name: "ai_api_calls_total",
			Help: "Total number of AI API calls per provider",
		}, []string{"provider", "model", "status_code"})

	apiCallsDuration = promauto.NewHistogramVec(
		prometheus.HistogramOpts{
			Name:    "ai_api_calls_duration_seconds",
			Help:    "Duration of AI API calls in seconds",
			Buckets: []float64{.1, .25, .5, 1, 2.5, 5, 10, 20, 30, 60},
		}, []string{"provider", "model"})
)

func NewAPIError(err error, statusCode int) *APIError {
	return &APIError{
		Err:        err,
		StatusCode: statusCode,
	}
}

// APIError represents an error returned by the AI API client and has an associated HTTP status code.
type APIError struct {
	Err        error
	StatusCode int
}

func (e *APIError) Error() string {
	return e.Err.Error()
}

func (e *APIError) Unwrap() error {
	return e.Err
}

// instrumentedClient is a wrapper around the Client interface that adds metrics instrumentation
type instrumentedClient struct {
	Client
	provider string
}

func (c *instrumentedClient) Message(ctx context.Context, req Request) (*Response, error) {
	statusCode := "200"
	start := time.Now()
	model := "unknown"
	resp, err := c.Client.Message(ctx, req)
	if err != nil {
		statusCode = "500"
		var apiErr *APIError
		if errors.As(err, &apiErr) {
			statusCode = strconv.Itoa(apiErr.StatusCode)
		}
	}
	cost := 0.0
	if resp != nil {
		model = resp.Model
		cost = resp.Usage.Cost
	}

	apiCallsTotal.WithLabelValues(c.provider, model, statusCode).Inc()
	duration := time.Since(start)
	apiCallsDuration.WithLabelValues(c.provider, model).Observe(duration.Seconds())
	logging.GetContextLogger(ctx).Info("AI API call completed",
		"provider", c.provider,
		"model", model,
		"status_code", statusCode,
		"duration_seconds", duration.Seconds(),
		"cost", cost)

	return resp, err
}

func instrumentClient(provider string, client Client) Client {
	return &instrumentedClient{client, provider}
}
