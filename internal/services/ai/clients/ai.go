package clients

import (
	"context"
	"database/sql"
	"errors"

	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	"github.com/akuityio/akuity-platform/internal/services/features"
	"github.com/akuityio/akuity-platform/internal/utils/ai"
	"github.com/akuityio/akuity-platform/models/client"
	"github.com/akuityio/akuity-platform/models/models"
)

type callerInfoKeyType struct{}

var callerInfoKey = callerInfoKeyType{}

type AIClientSet struct {
	clientsByProvider map[string]ai.Client
	repoSet           client.RepoSet
	db                *sql.DB
}

type CallerInfo struct {
	ConversationMeta *models.AIConversationMetadata
	OrganizationID   string
}

func WithCallerInfo(ctx context.Context, callerInfo CallerInfo) context.Context {
	return context.WithValue(ctx, callerInfoKey, callerInfo)
}

func (c *AIClientSet) Message(ctx context.Context, request ai.Request) (*ai.Response, error) {
	callerInfo, err := c.getCallerInfo(ctx)
	if err != nil {
		return nil, err
	}
	provider, limit, err := c.getOrgProvider(ctx, callerInfo.OrganizationID)
	if err != nil {
		return nil, err
	}
	currentUsage, err := features.GetOrgCurrentAIUsage(ctx, callerInfo.OrganizationID, c.repoSet)
	if err != nil {
		return nil, err
	}

	if currentUsage.Cost >= limit {
		return &ai.Response{
			Choices: []ai.Choice{{
				Message: ai.Message{
					Role:    ai.MessageRoleAssistant,
					Content: "Your organization has reached the AI usage limit. Please contact support to increase your limit.",
				},
			}},
		}, nil
	}

	aiClient, ok := c.clientsByProvider[provider]
	if !ok {
		return nil, status.Error(codes.InvalidArgument, "no AI client found for provider: "+provider)
	}
	resp, err := aiClient.Message(ctx, request)
	if err != nil {
		return nil, err
	}
	usage := models.AIUsage{InputTokens: resp.Usage.InputTokens, CachedInputTokens: resp.Usage.CachedInputTokens, OutputTokens: resp.Usage.OutputTokens, Cost: resp.Usage.Cost, InputTokenPrice: resp.Usage.InputTokenPrice}
	callerInfo.ConversationMeta.Usage = callerInfo.ConversationMeta.Usage.Plus(usage)
	if err := features.AddOrgAIUsage(ctx, callerInfo.OrganizationID, usage, c.db); err != nil {
		return nil, err
	}
	return resp, nil
}

func (c *AIClientSet) GetProvider(ctx context.Context) (string, error) {
	callerInfo, err := c.getCallerInfo(ctx)
	if err != nil {
		return "", err
	}

	provider, _, err := c.getOrgProvider(ctx, callerInfo.OrganizationID)
	if err != nil {
		return "", err
	}

	return provider, nil
}

func (c *AIClientSet) getOrgProvider(ctx context.Context, orgID string) (string, float64, error) {
	org, err := c.repoSet.Organizations().GetByID(ctx, orgID)
	if err != nil {
		return "", 0, err
	}
	spec, err := org.GetSpec()
	if err != nil {
		return "", 0, err
	}
	provider := ai.ProviderOpenAI
	if spec.AI.Provider != "" {
		provider = spec.AI.Provider
	}
	quotas, err := org.GetOrgQuota()
	if err != nil {
		return "", 0, err
	}

	return provider, quotas.MaxAiCostPerMonth, nil
}

func (c *AIClientSet) getCallerInfo(ctx context.Context) (CallerInfo, error) {
	callerInfo, ok := ctx.Value(callerInfoKey).(CallerInfo)
	if !ok {
		return CallerInfo{}, errors.New("caller info must be provided in context")
	}
	return callerInfo, nil
}

func (c *AIClientSet) AddClient(provider string, client ai.Client) {
	c.clientsByProvider[provider] = client
}

func NewAIClientSet(repoSet client.RepoSet, db *sql.DB) *AIClientSet {
	return &AIClientSet{repoSet: repoSet, db: db, clientsByProvider: map[string]ai.Client{}}
}
