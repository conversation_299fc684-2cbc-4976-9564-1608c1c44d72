package k8sresource

import (
	"context"
	"fmt"
	"strings"

	"github.com/lib/pq"
	"github.com/volatiletech/sqlboiler/v4/queries"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	_ "embed"
)

//go:embed image.sql
var listKubernetesImagesQuery string

var imageOrderByConfig = OrderByConfig{
	SupportedOrderByFields: map[string]string{
		"name":           "name",
		"tag":            "tag",
		"containerCount": "container_count",
		"cveCount":       "cve_count",
	},
	DefaultOrderBy: "container_count desc",
}

type OrderByConfig struct {
	SupportedOrderByFields map[string]string
	DefaultOrderBy         string
	Asc                    string
	Desc                   string
}

type Image struct {
	// The total number of images that match the query.
	ImageCount uint32 `boil:"image_count" json:"imageCount"`

	Name            string `boil:"name" json:"name"`
	Tag             string `boil:"tag" json:"tag"`
	ContainerCount  uint32 `boil:"container_count" json:"containerCount"`
	Digest          string `boil:"digest" json:"digest"`
	CVEs            string `boil:"cves" json:"cves"`
	CVECount        int32  `boil:"cve_count" json:"cveCount"`
	CVELastScanTime string `boil:"cve_last_scan_time" json:"cveLastScanTime"`
}

type ImageCount struct {
	ImageCount uint32 `boil:"image_count" json:"imageCount"`
}

func (s *Service) GetImage(ctx context.Context, instanceID string, clusterIDs []string, name, tag, digest string) (Image, error) {
	query, err := s.buildImageQuery(ctx, instanceID, clusterIDs, "", "", name, tag, digest, 0, 1)
	if err != nil {
		return Image{}, err
	}

	var images []Image
	if err := query.Bind(ctx, s.db, &images); err != nil {
		return Image{}, err
	}

	if len(images) == 0 {
		return Image{}, status.Errorf(codes.NotFound, "image %s:%s@%s not found", name, tag, digest)
	}

	return images[0], nil
}

func (s *Service) ListImages(ctx context.Context, instanceID string, clusterIDs []string, orderBy, nameContains, digest string, offset, limit int) ([]Image, error) {
	query, err := s.buildImageQuery(ctx, instanceID, clusterIDs, orderBy, nameContains, "", "", digest, offset, limit)
	if err != nil {
		return nil, err
	}

	var images []Image
	if err = query.Bind(ctx, s.db, &images); err != nil {
		return nil, err
	}

	return images, nil
}

func (s *Service) CountImages(ctx context.Context, instanceID string, clusterIDs []string, nameContains, digest string) (uint32, error) {
	query, err := s.buildImageQuery(ctx, instanceID, clusterIDs, "", nameContains, "", "", digest, 0, 1)
	if err != nil {
		return 0, err
	}
	var images []Image
	if err = query.Bind(ctx, s.db, &images); err != nil {
		return 0, err
	}

	if len(images) == 0 {
		return 0, nil
	}

	return images[0].ImageCount, nil
}

func (s *Service) buildImageQuery(ctx context.Context, instanceID string, clusterIDs []string, orderBy, nameContains, name, tag, digest string, offset, limit int) (*queries.Query, error) {
	args := []interface{}{
		s.organizationID,
		instanceID,
		pq.StringArray(clusterIDs),
		parseArg(nameContains),
		parseArg(name),
		parseArg(tag),
		parseArg(digest),
		offset,
		limit,
	}
	parsedOrderBy, err := parseOrderBy(orderBy, imageOrderByConfig)
	if err != nil {
		return nil, err
	}
	ordBySql := ""
	if parsedOrderBy != "" {
		ordBySql = fmt.Sprintf("ORDER BY %s", parsedOrderBy)
	}
	akiPermissionFilter := ""
	if s.enforcer != nil {
		filter, err := s.enforcer.GetK8SResourceListFilterSQL(ctx, instanceID, &args)
		if err != nil {
			return nil, err
		}
		akiPermissionFilter = fmt.Sprintf("INNER JOIN (select id from argo_cd_cluster_k8s_object where %s) as owners ON owners.id = argo_cd_cluster_k8s_object.owner_id", filter)
	}
	return queries.Raw(fmt.Sprintf(listKubernetesImagesQuery, akiPermissionFilter, ordBySql), args...), nil
}

func parseArg(arg string) *string {
	if arg == "" {
		return nil
	}
	output := arg
	if arg == ExplicitlyEmpty {
		output = ""
	}
	return &output
}

// parseOrderBy parses the orderBy string from the request and return the sql order by clause.
// The string value should follow SQL syntax: comma separated list of fields. For example: "foo,bar". The default sorting order is ascending.
// To specify descending order for a field, a suffix " desc" should be appended to the field name. For example: "foo desc,bar".
// Redundant space characters in the syntax are insignificant. "foo,bar desc" and "  foo ,  bar  desc  " are equivalent.
func parseOrderBy(orderBy string, config OrderByConfig) (string, error) {
	if orderBy == "" {
		return config.DefaultOrderBy, nil
	}

	var parsedOrderBy []string
	fields := strings.Split(orderBy, ",")
	for _, field := range fields {
		field = strings.TrimSpace(field)
		if field == "" {
			continue
		}
		parts := strings.Fields(field)
		if len(parts) > 2 {
			return "", status.Errorf(codes.InvalidArgument, "invalid order by syntax: %s", field)
		}
		colName, ok := config.SupportedOrderByFields[parts[0]]
		if !ok {
			supported := make([]string, 0, len(config.SupportedOrderByFields))
			for k := range config.SupportedOrderByFields {
				supported = append(supported, k)
			}
			return "", status.Errorf(codes.InvalidArgument, "unsupported order by field: %s, supported fields: %s", parts[0], strings.Join(supported, ", "))
		}
		if len(parts) == 1 || (len(parts) == 2 && parts[1] == "asc") {
			asc := ""
			if config.Asc != "" {
				asc = " " + config.Asc
			}
			parsedOrderBy = append(parsedOrderBy, colName+asc)
		} else if len(parts) == 2 && parts[1] == "desc" {
			desc := " desc"
			if config.Desc != "" {
				desc = " " + config.Desc
			}
			parsedOrderBy = append(parsedOrderBy, colName+desc)
		} else {
			return "", status.Errorf(codes.InvalidArgument, "invalid order by syntax: %s", field)
		}
	}
	if len(parsedOrderBy) == 0 {
		return config.DefaultOrderBy, nil
	}
	return strings.Join(parsedOrderBy, ", "), nil
}
