package k8sresource

import (
	"context"
	"database/sql"
	"errors"
	"fmt"

	"github.com/samber/lo"
	"github.com/volatiletech/null/v8"
	"github.com/volatiletech/sqlboiler/v4/queries/qm"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"k8s.io/apimachinery/pkg/runtime/schema"

	"github.com/akuityio/akuity-platform/models/models"
	organizationv1 "github.com/akuityio/akuity-platform/pkg/api/gen/organization/v1"
	k8sv1 "github.com/akuityio/akuity-platform/pkg/api/gen/types/k8s/v1"
)

const (
	CPURequest = "cpuRequest"
	CPULimit   = "cpuLimit"
	CPUUsage   = "cpuUsage"
	MemRequest = "memoryRequest"
	MemLimit   = "memoryLimit"
	MemUsage   = "memoryUsage"
	StartTime  = "startTime"

	// ExplicitlyEmpty On API request url, if it is requesting an empty value, it must be explicitly declared
	ExplicitlyEmpty = "_empty"
)

var Container = schema.GroupVersionKind{Group: "dashboard.akuity.io", Version: "v1alpha1", Kind: "Container"}

func (s *Service) GetContainer(ctx context.Context, instanceID, clusterID, containerID string) (*models.ArgoCDClusterK8SObject, error) {
	res, err := s.ArgoCDClusterK8sObjects(
		models.ArgoCDClusterK8SObjectWhere.InstanceID.EQ(instanceID),
		models.ArgoCDClusterK8SObjectWhere.ClusterID.EQ(clusterID),
		models.ArgoCDClusterK8SObjectWhere.Group.EQ(null.StringFrom(Container.Group)),
		models.ArgoCDClusterK8SObjectWhere.Kind.EQ(null.StringFrom(Container.Kind)),
	).GetByID(ctx, containerID)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, status.Errorf(codes.NotFound, "container %s not found", containerID)
		}
		return nil, err
	}
	return res, nil
}

func (s *Service) ListContainerResources(ctx context.Context, instanceID, podID string, clusterIDs []string,
	image, imageTag, imageDigest, orderBy string, offset, limit int,
	nameContains string, containerStatus organizationv1.ContainerStatus, containerType organizationv1.ContainerType,
) ([]*models.ArgoCDClusterK8SObject, error) {
	mods, err := s.listContainersMods(ctx, instanceID, podID, clusterIDs, image, imageTag, imageDigest,
		orderBy, offset, limit, nameContains, containerStatus, containerType, false)
	if err != nil {
		return nil, err
	}

	resources, err := s.ArgoCDClusterK8sObjects(mods...).ListAll(ctx)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, nil
		}
		return nil, status.Errorf(codes.Internal, "failed to list containers: %v", err)
	}
	return resources, nil
}

func (s *Service) CountContainerResources(ctx context.Context, instanceID, podID string, clusterIDs []string, image, imageTag, imageDigest string,
	nameContains string, containerStatus organizationv1.ContainerStatus, containerType organizationv1.ContainerType,
) (int64, error) {
	mods, err := s.listContainersMods(ctx, instanceID, podID, clusterIDs, image, imageTag, imageDigest, "", 0, 0, nameContains, containerStatus, containerType, true)
	if err != nil {
		return 0, err
	}

	count, err := s.ArgoCDClusterK8sObjects(mods...).Count(ctx)
	if err != nil {
		return 0, status.Errorf(codes.Internal, "failed to count containers: %v", err)
	}
	return count, nil
}

func (s *Service) listContainersMods(ctx context.Context, instanceID, podID string, clusterIDs []string,
	image, imageTag, imageDigest, orderBy string, offset, limit int,
	nameContains string, containerStatus organizationv1.ContainerStatus, containerType organizationv1.ContainerType,
	count bool,
) ([]qm.QueryMod, error) {
	var mods []qm.QueryMod
	mods = append(mods, models.ArgoCDClusterK8SObjectWhere.Group.EQ(null.StringFrom(Container.Group)))
	mods = append(mods, models.ArgoCDClusterK8SObjectWhere.Kind.EQ(null.StringFrom(Container.Kind)))
	if instanceID != "" {
		mods = append(mods, models.ArgoCDClusterK8SObjectWhere.InstanceID.EQ(instanceID))
	}
	if len(clusterIDs) > 0 {
		mods = append(mods, models.ArgoCDClusterK8SObjectWhere.ClusterID.IN(clusterIDs))
	}

	if podID != "" {
		mods = append(mods, models.ArgoCDClusterK8SObjectWhere.OwnerID.EQ(null.StringFrom(podID)))
	}

	if image != "" {
		mods = append(mods, qm.Where("columns->>'image' = ?", image))
	}

	if imageTag != "" {
		mods = append(mods, qm.Where("columns->>'tag' = ?", imageTag))
	}

	if imageDigest != "" {
		// Request empty image digest must be explicitly declared
		if imageDigest == ExplicitlyEmpty {
			imageDigest = ""
		}
		mods = append(mods, qm.Where("coalesce(columns->>'digest', '') = ?", imageDigest))
	}

	if nameContains != "" {
		mods = append(mods, models.ArgoCDClusterK8SObjectWhere.Name.ILIKE("%"+nameContains+"%"))
	}

	if containerStatus != organizationv1.ContainerStatus_CONTAINER_STATUS_UNSPECIFIED {
		statusStr := ""
		switch containerStatus {
		case organizationv1.ContainerStatus_CONTAINER_STATUS_RUNNING:
			statusStr = "running"
		case organizationv1.ContainerStatus_CONTAINER_STATUS_PENDING:
			statusStr = "waiting"
		case organizationv1.ContainerStatus_CONTAINER_STATUS_COMPLETED:
			statusStr = "completed"
		case organizationv1.ContainerStatus_CONTAINER_STATUS_ERROR:
			statusStr = "terminated"
		}
		mods = append(mods, qm.Where("columns->>'state' = ?", statusStr))
	}

	if containerType != organizationv1.ContainerType_CONTAINER_TYPE_UNSPECIFIED {
		containerTypeStr := ""
		switch containerType {
		case organizationv1.ContainerType_CONTAINER_TYPE_CONTAINER:
			containerTypeStr = "container"
		case organizationv1.ContainerType_CONTAINER_TYPE_INIT_CONTAINER:
			containerTypeStr = "initContainer"
		case organizationv1.ContainerType_CONTAINER_TYPE_SIDECAR_CONTAINER:
			containerTypeStr = "sidecarContainer"
		case organizationv1.ContainerType_CONTAINER_TYPE_EPHEMERAL_CONTAINER:
			containerTypeStr = "ephemeralContainer"
		}
		mods = append(mods, qm.Where("columns->>'containerType' = ?", containerTypeStr))
	}

	if !count {
		cfg := OrderByConfig{
			SupportedOrderByFields: map[string]string{
				CPULimit:   "(columns->>'limits.cpu')::numeric",
				CPURequest: "(columns->>'requests.cpu')::numeric",
				MemLimit:   "(columns->>'limits.memory')::bigint",
				MemRequest: "(columns->>'requests.memory')::bigint",
				CPUUsage:   "(columns->>'usage.cpu')::numeric",
				MemUsage:   "(columns->>'usage.memory')::bigint",
				StartTime:  "columns->>'startTime'",
			},
			DefaultOrderBy: "creation_timestamp desc",
			Desc:           "desc nulls last",
			Asc:            "asc nulls first",
		}
		pob, err := parseOrderBy(orderBy, cfg)
		if err != nil {
			return nil, err
		}
		if pob != "" {
			mods = append(mods, qm.OrderBy(pob))
		}

		if limit > 0 {
			mods = append(mods, qm.Limit(limit))
		}
		if offset > 0 {
			mods = append(mods, qm.Offset(offset))
		}
	}
	if s.enforcer != nil {
		var args []interface{}
		filter, err := s.enforcer.GetK8SResourceListFilterSQL(ctx, instanceID, &args)
		if err != nil {
			return nil, err
		}
		mods = append(mods,
			qm.InnerJoin(fmt.Sprintf("(select id from argo_cd_cluster_k8s_object where %s) as owners ON owners.id = argo_cd_cluster_k8s_object.owner_id", filter), args...),
		)
	}
	return mods, nil
}

type ContainerColumns struct {
	Tag            string   `json:"tag,omitempty"`
	Image          string   `json:"image,omitempty"`
	Ready          string   `json:"ready,omitempty"`
	State          string   `json:"state,omitempty"`
	PodName        string   `json:"podName,omitempty"`
	ContainerType  string   `json:"containerType,omitempty"`
	LimitsMemory   *float64 `json:"limits.memory,omitempty"`
	RequestsMemory *float64 `json:"requests.memory,omitempty"`
	LimitsCPU      *float64 `json:"limits.cpu,omitempty"`
	RequestsCPU    *float64 `json:"requests.cpu,omitempty"`
	Digest         string   `json:"digest,omitempty"`
	UsageCPU       *float64 `json:"usage.cpu,omitempty"`
	UsageMemory    *float64 `json:"usage.memory,omitempty"`
	StartTime      *string  `json:"startTime,omitempty"`
}

func (s *Service) PodContainersForWorkload(ctx context.Context, instanceID, clusterID, resourceID string) ([]*organizationv1.PodInfo, error) {
	pods, err := s.findPodsFromWorkloads(ctx, instanceID, clusterID, resourceID)
	if err != nil {
		return nil, err
	}

	podContainers := map[string][]*organizationv1.IDInfo{}
	podMap := map[string]*models.ArgoCDClusterK8SObject{}
	for _, pod := range pods {
		podContainers[pod.ID] = nil
		podMap[pod.ID] = pod
	}

	ids := lo.Keys(podContainers)
	containers, err := s.ArgoCDClusterK8sObjects(
		models.ArgoCDClusterK8SObjectWhere.InstanceID.EQ(instanceID),
		models.ArgoCDClusterK8SObjectWhere.ClusterID.EQ(clusterID),
		models.ArgoCDClusterK8SObjectWhere.OwnerID.IN(ids),
		models.ArgoCDClusterK8SObjectWhere.Kind.EQ(null.StringFrom(Container.Kind)),
		models.ArgoCDClusterK8SObjectWhere.Group.EQ(null.StringFrom(Container.Group)),
	).ListAll(ctx)
	if err != nil {
		return nil, err
	}

	for _, container := range containers {
		if _, ok := podContainers[container.OwnerID.String]; !ok {
			continue
		}
		podContainers[container.OwnerID.String] = append(podContainers[container.OwnerID.String], &organizationv1.IDInfo{
			Name: container.Name,
			Id:   container.ID,
		})
	}

	res := lo.MapToSlice(podContainers, func(key string, value []*organizationv1.IDInfo) *organizationv1.PodInfo {
		pod := podMap[key]
		return &organizationv1.PodInfo{
			Pod:        &organizationv1.IDInfo{Name: pod.Name, Id: pod.ID},
			Containers: value,
		}
	})
	return res, nil
}

func (s *Service) findPodsFromWorkloads(ctx context.Context, instanceID, clusterID, resourceID string) ([]*models.ArgoCDClusterK8SObject, error) {
	resource, err := s.ArgoCDClusterK8sObjects(
		models.ArgoCDClusterK8SObjectWhere.InstanceID.EQ(instanceID),
		models.ArgoCDClusterK8SObjectWhere.ClusterID.EQ(clusterID),
		models.ArgoCDClusterK8SObjectWhere.ID.EQ(resourceID),
	).One(ctx)
	if err != nil {
		return nil, err
	}
	if !IsWorkload(resource.Group.String, resource.Kind.String) {
		return nil, nil
	}
	if resource.IsPod() {
		return []*models.ArgoCDClusterK8SObject{resource}, nil
	}
	var pods []*models.ArgoCDClusterK8SObject
	workloadIDs := []string{resourceID}
	// The maxDepth is used to avoid infinite db calls if there is any bugs in the follow for loop causing it never ends.
	// The maxDepth is e.g., Cronjob -> Job -> Pod, or Deployment -> ReplicaSet -> Pod, so we set it to 2.
	maxDepth := 2
	for depth := 0; len(workloadIDs) > 0 && depth < maxDepth; depth++ {
		var childIDs []string
		resources, err := s.ArgoCDClusterK8sObjects(
			models.ArgoCDClusterK8SObjectWhere.InstanceID.EQ(instanceID),
			models.ArgoCDClusterK8SObjectWhere.ClusterID.EQ(clusterID),
			models.ArgoCDClusterK8SObjectWhere.OwnerID.IN(workloadIDs),
		).ListAll(ctx)
		if err != nil {
			return nil, err
		}
		for _, r := range resources {
			if !IsWorkload(r.Group.String, r.Kind.String) {
				continue
			}
			if r.IsPod() {
				pods = append(pods, r)
			} else {
				childIDs = append(childIDs, r.ID)
			}
		}
		workloadIDs = childIDs
	}
	return pods, nil
}

func IsWorkload(group, kind string) bool {
	gk := GroupKind{Group: group, Kind: kind}
	v := gkToCategory[gk]
	return v == k8sv1.ResourceCategory_RESOURCE_CATEGORY_WORKLOADS
}
