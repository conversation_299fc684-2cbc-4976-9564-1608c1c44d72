package extensions

import (
	"context"
	"encoding/json"
	"fmt"
	"hash/fnv"
	"io"
	"net/http"
	"os"

	"github.com/volatiletech/null/v8"
	"github.com/volatiletech/sqlboiler/v4/boil"
	"golang.org/x/mod/semver"

	"github.com/akuityio/akuity-platform/internal/config"
	"github.com/akuityio/akuity-platform/internal/services/features"
	"github.com/akuityio/akuity-platform/internal/utils/ai"
	httputil "github.com/akuityio/akuity-platform/internal/utils/http"
	"github.com/akuityio/akuity-platform/internal/version"
	"github.com/akuityio/akuity-platform/models/client"
	"github.com/akuityio/akuity-platform/models/models"
	argocdv1 "github.com/akuityio/akuity-platform/pkg/api/gen/argocd/v1"
	extensionv1 "github.com/akuityio/akuity-platform/pkg/api/gen/extension/v1"
)

const (
	argoExtensionDir  = "./portal/ui/extensions-build/assets"
	kargoExtensionDir = "./portal/ui/kargo-extensions-build/assets"
)

func instanceExtensionsURL(instance *models.ArgoCDInstance) string {
	return fmt.Sprintf("http://argocd-server.argocd-%s.svc.cluster.local/extensions.js", instance.ID)
}

func newCrossplaneExtensionSetting(spec models.InstanceConfigSpec) *argocdv1.CrossplaneExtension {
	resources := make([]*argocdv1.CrossplaneExtensionResource, 0, len(spec.CrossplaneExtension.Resources))
	for _, val := range spec.CrossplaneExtension.Resources {
		resources = append(resources, &argocdv1.CrossplaneExtensionResource{
			Group: val.Group,
		})
	}
	return &argocdv1.CrossplaneExtension{
		Resources: resources,
	}
}

func newAkuityIntelligenceExtension(spec models.InstanceConfigSpec, modelVersion string) *argocdv1.AkuityIntelligenceExtension {
	return &argocdv1.AkuityIntelligenceExtension{
		Enabled:                  spec.AkuityIntelligenceExtension.Enabled,
		AllowedUsernames:         spec.AkuityIntelligenceExtension.AllowedUsernames,
		AllowedGroups:            spec.AkuityIntelligenceExtension.AllowedGroups,
		AiSupportEngineerEnabled: spec.AkuityIntelligenceExtension.AISupportEngineerEnabled,
		ModelVersion:             modelVersion,
	}
}

func newKubeVisionConfig(spec models.KubeVisionConfig) *argocdv1.KubeVisionConfig {
	return &argocdv1.KubeVisionConfig{
		CveScanConfig: &argocdv1.CveScanConfig{
			ScanEnabled:    spec.CveScanConfig.ScanEnabled,
			RescanInterval: spec.CveScanConfig.RescanInterval,
		},
	}
}

func newApplicationSetExtension(spec models.InstanceConfigSpec) *argocdv1.ApplicationSetExtension {
	return &argocdv1.ApplicationSetExtension{
		Enabled: spec.ApplicationSetExtension.Enabled,
	}
}

func getAIModelVersion(ctx context.Context, repoSet client.RepoSet, organizationID string) (string, error) {
	org, err := repoSet.Organizations().GetByID(ctx, organizationID)
	if err != nil {
		return ai.GetModelVersion(""), err
	}

	orgSpec, err := org.GetSpec()
	if err != nil {
		return ai.GetModelVersion(""), err
	}

	return ai.GetModelVersion(orgSpec.AI.Provider), nil
}

func ExtensionsJSRequestHandler(db boil.ContextExecutor, cfg config.PortalServerConfig) http.HandlerFunc {
	return func(w http.ResponseWriter, req *http.Request) {
		repoSet := client.NewRepoSet(db)
		featSvc := features.NewService(repoSet, db, config.IsSelfHosted, config.IsSelfHostedReleaseBuild, cfg.FeatureGatesSource, config.GetLicense())

		reqHost := req.Header.Get("x-forwarded-host")
		instance, err := repoSet.ArgoCDInstances(models.ArgoCDInstanceWhere.StatusHostname.EQ(null.StringFrom(reqHost))).One(req.Context())
		if err != nil {
			http.Error(w, err.Error(), http.StatusInternalServerError)
			return
		}
		featureStatuses := featSvc.GetFeatureStatuses(req.Context(), &instance.OrganizationOwner)

		instanceConfig, err := models.ArgoCDInstanceConfigs(models.ArgoCDInstanceConfigWhere.InstanceID.EQ(instance.ID)).One(req.Context(), db)
		if err != nil {
			http.Error(w, err.Error(), http.StatusInternalServerError)
			return
		}
		spec, err := instanceConfig.GetSpec()
		if err != nil {
			http.Error(w, err.Error(), http.StatusInternalServerError)
			return
		}

		modelVersion, err := getAIModelVersion(req.Context(), repoSet, instance.OrganizationOwner)
		if err != nil {
			http.Error(w, err.Error(), http.StatusInternalServerError)
			return
		}

		if req.URL.Query().Encode() == "" {
			httputil.SetNoCacheHeaders(w, false)
			if hash, err := getSpecHash(spec); err != nil {
				http.Error(w, err.Error(), http.StatusInternalServerError)
				return
			} else {
				http.Redirect(w, req, req.URL.String()+"?"+hash, http.StatusTemporaryRedirect)
				return
			}
		}

		w.Header().Set("Cache-Control", "public, max-age=31536000, immutable")

		resp, err := http.Get(instanceExtensionsURL(instance))
		if err != nil {
			http.Error(w, err.Error(), http.StatusInternalServerError)
			return
		}
		defer func(Body io.ReadCloser) { _ = Body.Close() }(resp.Body)
		extensions, err := io.ReadAll(resp.Body)
		if err != nil {
			http.Error(w, err.Error(), http.StatusInternalServerError)
			return
		}

		// Only inject Akuity Extension(s) if enabled and version is greater than 2.5.4
		if semver.Compare(instanceConfig.Version.String, "2.5.4") >= 0 &&
			featureStatuses.GetAkuityArgocdExtensions().Enabled() &&
			(spec.AuditExtensionEnabled ||
				spec.SyncHistoryExtensionEnabled ||
				spec.AssistantExtensionEnabled ||
				len(spec.CrossplaneExtension.Resources) > 0 ||
				spec.AkuityIntelligenceExtension.Enabled ||
				spec.ApplicationSetExtension.Enabled) {
			akuityExtensions, err := os.ReadFile(argoExtensionDir + "/extensions.js")
			if err != nil {
				http.Error(w, err.Error(), http.StatusInternalServerError)
				return
			}

			data, err := json.Marshal(struct {
				extensionv1.Settings
			}{
				Settings: extensionv1.Settings{
					OrganizationId:              instance.OrganizationOwner,
					InstanceId:                  instance.ID,
					InstanceVersion:             instanceConfig.Version.String,
					AuditExtensionEnabled:       spec.AuditExtensionEnabled,
					SyncHistoryExtensionEnabled: spec.SyncHistoryExtensionEnabled,
					AssistantExtensionEnabled:   spec.AssistantExtensionEnabled,
					CrossplaneExtension:         newCrossplaneExtensionSetting(spec),
					AkuityIntelligenceExtension: newAkuityIntelligenceExtension(spec, modelVersion),
					FeatureStatuses:             featureStatuses,
					Config: &extensionv1.Config{
						Env:                      cfg.ENV,
						ArgocdExtensionSentryDsn: cfg.ArgocdUISentryDSN,
					},
					KubeVisionConfig:        newKubeVisionConfig(spec.KubeVisionConfig),
					ApplicationSetExtension: newApplicationSetExtension(spec),
				},
			})
			if err != nil {
				http.Error(w, err.Error(), http.StatusInternalServerError)
				return
			}
			if req.Header.Get("Accept") == "application/json" {
				w.Header().Set("Content-Type", "application/json")
				_, _ = w.Write(data)
				return
			}

			extensions = []byte(
				fmt.Sprintf(`window.__akuity = %s;`, string(data)) + "\n" +
					string(extensions) + "\n" +
					string(akuityExtensions))
		}

		w.Header().Set("Content-Type", "application/javascript")

		_, _ = w.Write(extensions)
	}
}

func KargoExtensionsJSRequestHandler(db boil.ContextExecutor, cfg config.PortalServerConfig) http.HandlerFunc {
	return func(w http.ResponseWriter, req *http.Request) {
		repoSet := client.NewRepoSet(db)
		featSvc := features.NewService(repoSet, db, config.IsSelfHosted, config.IsSelfHostedReleaseBuild, cfg.FeatureGatesSource, config.GetLicense())

		reqHost := req.Header.Get("x-forwarded-host")
		instance, err := repoSet.KargoInstances(models.KargoInstanceWhere.StatusHostname.EQ(null.StringFrom(reqHost))).One(req.Context())
		if err != nil {
			http.Error(w, err.Error(), http.StatusInternalServerError)
			return
		}
		featureStatuses := featSvc.GetFeatureStatuses(req.Context(), &instance.OrganizationOwner.String)
		instanceConfig, err := models.KargoInstanceConfigs(models.KargoInstanceConfigWhere.InstanceID.EQ(instance.ID)).One(req.Context(), db)
		if err != nil {
			http.Error(w, err.Error(), http.StatusInternalServerError)
			return
		}
		spec, err := instanceConfig.GetSpec()
		if err != nil {
			http.Error(w, err.Error(), http.StatusInternalServerError)
			return
		}

		modelVersion, err := getAIModelVersion(req.Context(), repoSet, instance.OrganizationOwner.String)
		if err != nil {
			http.Error(w, err.Error(), http.StatusInternalServerError)
			return
		}
		if spec.AkuityIntelligence != nil {
			spec.AkuityIntelligence.ModelVersion = modelVersion
		}
		data, err := json.Marshal(struct {
			Settings extensionv1.KargoSettings
		}{
			Settings: extensionv1.KargoSettings{
				OrganizationId:     instance.OrganizationOwner.String,
				InstanceId:         instance.ID,
				FeatureStatuses:    featureStatuses,
				AkuityIntelligence: spec.AkuityIntelligence,
			},
		})
		if err != nil {
			http.Error(w, err.Error(), http.StatusInternalServerError)
			return
		}

		if req.Header.Get("Accept") == "application/json" {
			w.Header().Set("Content-Type", "application/json")
			_, _ = w.Write(data)
			return
		}

		result := ""
		if featureStatuses.GetKargoEnterprise().Enabled() {
			kargoExtensionsJS, err := os.ReadFile(kargoExtensionDir + "/kargo-extensions.js")
			if err != nil {
				http.Error(w, err.Error(), http.StatusInternalServerError)
				return
			}
			result = string(kargoExtensionsJS)
		}

		w.Header().Set("Content-Type", "application/javascript")
		_, _ = fmt.Fprintf(w,
			"window.__akuity = %s;\n%s", string(data), string(result))
	}
}

func getSpecHash(spec models.InstanceConfigSpec) (string, error) {
	data, err := json.Marshal(map[string]interface{}{
		"extensions":                  spec.Extensions,
		"version":                     version.GetVersion(),
		"crossplaneExtension":         spec.CrossplaneExtension,
		"auditExtensionEnabled":       spec.AuditExtensionEnabled,
		"syncHistoryExtensionEnabled": spec.SyncHistoryExtensionEnabled,
		"assistantExtensionEnabled":   spec.AssistantExtensionEnabled,
		"akuityIntelligenceExtension": spec.AkuityIntelligenceExtension,
		"kubeVisionConfig":            spec.KubeVisionConfig,
		"applicationSetExtension":     spec.ApplicationSetExtension,
	})
	if err != nil {
		return "", err
	}
	h := fnv.New32a()
	if _, err = h.Write(data); err != nil {
		return "", err
	}
	return fmt.Sprintf("%x", h.Sum32()), nil
}
