{"name": "akuity-platform", "version": "0.0.1", "scripts": {"dev": "CHECK_CIRCULAR_DEPENDENCY=true webpack serve --config ./config/webpack.dev.js", "dev:extensions": "NODE_TLS_REJECT_UNAUTHORIZED=0 webpack serve --config ./config/extensions/webpack.dev.js", "dev:kargo-extensions": "NODE_TLS_REJECT_UNAUTHORIZED=0 webpack serve --config ./config/kargo-extensions/webpack.dev.js", "build": "pnpm run build:ui && pnpm run build:extensions && pnpm run build:kargo-extensions", "build:ui": "webpack build --config ./config/webpack.prod.js", "build:extensions": "webpack build --config ./config/extensions/webpack.prod.js", "build:kargo-extensions": "webpack build --config ./config/kargo-extensions/webpack.prod.js", "lint": "eslint --ignore-path .gitignore . --max-warnings=0", "lint:fix": "eslint --ignore-path .gitignore . --fix", "test": "vitest run --coverage", "test:watch": "vitest -w", "typecheck": "tsc --project ./tsconfig.json --noEmit", "generate:schema": "TS_NODE_PROJECT='./tsconfig.json' node --experimental-specifier-resolution=node --loader ./ts-node-loader.js ./scripts/generate-dex-json-schema.ts ../../models/util/validation/dex-schema.json", "generate:routing-paths": "TS_NODE_PROJECT='./tsconfig.json' node --experimental-specifier-resolution=node --loader ./ts-node-loader.js ./scripts/generate-routing-paths.ts ../../portal/server/routing-paths.json", "generate:role-policies": "TS_NODE_PROJECT='./tsconfig.json' node --experimental-specifier-resolution=node --loader ./ts-node-loader.js ./scripts/generate-custom-role-admin-policies.ts", "generate:permission-types": "TS_NODE_PROJECT='./tsconfig.json' node --experimental-specifier-resolution=node --loader ./ts-node-loader.js ./scripts/generate-permission-types.ts", "generate:api-types": "swagger-typescript-api generate --no-client -p ../../docs/generated/swagger/apidocs.swagger.yaml -o src/lib/apiclient -n generated.ts", "generate:artifacthub-api-types": "swagger-typescript-api generate --no-client -p https://artifacthub.io/docs/api/openapi.yaml -o src/lib/apiclient -n generated_artifacthub.ts", "generate:artifacthub-api-schema": "ts-to-zod ./src/lib/apiclient/generated_artifacthub.ts ./src/lib/apiclient/generated_artifacthub_zod_schema.ts && eslint ./src/lib/apiclient/generated_artifacthub_zod_schema.ts --fix", "webpack-helpers": "node ./scripts/print-webpack-helpers"}, "devDependencies": {"@babel/core": "7.27.7", "@babel/preset-env": "7.27.2", "@babel/preset-react": "7.27.1", "@babel/preset-typescript": "7.27.1", "@pmmmwh/react-refresh-webpack-plugin": "0.6.1", "@sentry/webpack-plugin": "3.5.0", "@testing-library/jest-dom": "6.6.3", "@testing-library/react": "16.3.0", "@testing-library/user-event": "14.6.1", "@types/css-tree": "2.3.10", "@types/cssbeautify": "0.3.5", "@types/dagre": "0.7.53", "@types/google-protobuf": "3.15.12", "@types/js-cookie": "3.0.6", "@types/json-merge-patch": "1.0.0", "@types/json-schema": "7.0.15", "@types/node": "22.15.21", "@types/papaparse": "5.3.16", "@types/react": "18.3.12", "@types/react-dom": "18.3.1", "@types/react-gtm-module": "2.0.4", "@types/semver": "7.7.0", "@types/use-sync-external-store": "1.5.0", "@typescript-eslint/eslint-plugin": "8.35.1", "@typescript-eslint/parser": "8.35.1", "@vitejs/plugin-react": "4.6.0", "@vitest/coverage-v8": "3.2.4", "autoprefixer": "10.4.21", "babel-loader": "10.0.0", "circular-dependency-plugin": "5.2.2", "combined-stream": "1.0.8", "copy-webpack-plugin": "13.0.0", "css-loader": "7.1.2", "cssnano": "7.0.7", "debug": "4.4.1", "dotenv": "16.5.0", "eslint": "8.57.1", "eslint-config-prettier": "9.1.0", "eslint-plugin-import": "2.32.0", "eslint-plugin-prettier": "5.5.1", "eslint-plugin-react": "7.37.5", "file-loader": "6.2.0", "fork-ts-checker-webpack-plugin": "9.1.0", "html-webpack-plugin": "5.6.3", "less-loader": "12.3.0", "mini-css-extract-plugin": "2.9.2", "postcss": "8.5.6", "postcss-load-config": "6.0.1", "postcss-loader": "8.1.1", "prettier": "3.6.2", "react-refresh": "0.17.0", "speed-measure-webpack-plugin": "1.5.0", "style-loader": "4.0.0", "swagger-typescript-api": "13.2.7", "sync-request": "6.1.0", "tailwindcss": "3.4.14", "terser-webpack-plugin": "5.3.14", "ts-node": "10.9.2", "ts-to-zod": "3.15.0", "tsconfig-paths": "4.2.0", "tsconfig-paths-webpack-plugin": "4.2.0", "typescript": "5.8.3", "vi-fetch": "0.8.0", "vite-tsconfig-paths": "5.1.4", "vitest": "3.2.4", "webpack": "5.99.9", "webpack-bundle-analyzer": "4.10.2", "webpack-cli": "6.0.1", "webpack-dev-server": "5.2.2", "webpack-merge": "6.0.1", "worker-loader": "3.0.8"}, "type": "module", "dependencies": {"@buf/googleapis_googleapis.bufbuild_es": "1.4.1-20250303202749-751cbe31638d.2", "@bufbuild/protobuf": "1.10.0", "@fortawesome/fontawesome-svg-core": "6.7.2", "@fortawesome/free-brands-svg-icons": "6.7.2", "@fortawesome/free-regular-svg-icons": "~6.7.2", "@fortawesome/free-solid-svg-icons": "6.7.2", "@fortawesome/react-fontawesome": "0.2.2", "@hookform/resolvers": "3.9.1", "@lukeed/uuid": "2.0.1", "@lukemorales/query-key-factory": "1.3.4", "@r2wc/react-to-web-component": "2.0.4", "@sentry/react": "9.34.0", "@tanstack/react-query": "5.81.5", "@xyflow/react": "12.8.1", "ace-builds": "1.43.0", "ace-linters": "1.7.0", "ajv": "8.17.1", "antd": "5.26.3", "bcryptjs-react": "2.4.6", "buffer": "6.0.3", "casbin": "5.38.0", "chart.js": "4.5.0", "chartjs-adapter-moment": "1.0.1", "chartjs-chart-treemap": "3.1.0", "chartjs-plugin-zoom": "2.2.0", "classnames": "2.5.1", "compressorjs": "1.2.1", "css-tree": "3.1.0", "cssbeautify": "0.3.1", "dagre": "0.8.5", "dompurify": "3.2.6", "eventsource": "4.0.0", "git-url-parse": "16.1.0", "history": "5.3.0", "js-cookie": "3.0.5", "json-merge-patch": "1.0.2", "marked": "15.0.12", "moment": "2.30.1", "papaparse": "5.5.3", "rc-picker": "4.11.3", "react": "18.3.1", "react-ace": "14.0.1", "react-chartjs-2": "5.3.0", "react-diff-viewer-continued": "3.4.0", "react-dom": "18.3.1", "react-gtm-module": "2.0.11", "react-hook-form": "7.59.0", "react-router-dom": "6.28.0", "semver": "7.7.2", "use-debounce": "10.0.5", "use-sync-external-store": "1.5.0", "yaml": "2.8.0", "zod": "3.25.67", "zod-to-json-schema": "3.24.6"}, "engines": {"pnpm": ">=9.3.0"}, "packageManager": "pnpm@9.3.0"}