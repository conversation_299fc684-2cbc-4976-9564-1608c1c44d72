import * as Sentry from '@sentry/react';
import { QueryClientProvider } from '@tanstack/react-query';
import { ConfigProvider as MainConfigProvider } from 'antd';
import ConfigProvider from 'antd/lib/config-provider';
import {
  Chart as ChartJS,
  ArcElement,
  Tooltip,
  Legend,
  CategoryScale,
  LinearScale,
  TimeScale,
  BarElement,
  PointElement,
  LineElement
} from 'chart.js';
import { TreemapController, TreemapElement } from 'chartjs-chart-treemap';
import zoomPlugin from 'chartjs-plugin-zoom';
import React from 'react';
import {
  Route,
  Navigate,
  createBrowserRouter,
  createRoutesFromElements,
  RouterProvider,
  Outlet
} from 'react-router-dom';

import { paths } from '@ui/config/paths';
import { queryClient } from '@ui/config/query-client';
import { theme } from '@ui/config/theme';
import { InstanceDetailsPage, InstancesListPage, NewInstancePage } from '@ui/instances';
import { Layout } from '@ui/lib/components/layout/layout';
import { Loading } from '@ui/lib/components/loading';
import { MainContextProvider } from '@ui/lib/context/main-context-provider';
import { OrganizationDetailsPage, OrganizationJoinPage } from '@ui/organizations';
import { OrganizationsSettingsPage } from '@ui/organizations/organizations-settings';
import { AccountPage } from '@ui/views/account/account-page';
import { ErrorPage } from '@ui/views/error-page';
import { MarketplaceOnboard } from '@ui/views/marketplace-onboard/marketplace-onboard';

import '@ui/app.less';
import { ClustersAddonDetailsPage } from './feature/argocd/addons/clusters-addon-details/clusters-addon-details-page';
import { ArgoCDMetricsPage } from './feature/argocd/argocd-metrics-page';
import { ArgoCDSettings } from './feature/argocd/settings/argocd-settings';
import { Demo } from './feature/demo/demo';
import { CreateKargoInstance } from './feature/kargo/create-kargo-instance';
import { KargoInstance } from './feature/kargo/kargo-instance/kargo-instance';
import { KargoInstanceSettings } from './feature/kargo/kargo-instance/settings/kargo-instance-settings';
import { KargoInstances } from './feature/kargo/kargo-instances';
import { KargoMetricsPage } from './feature/kargo/kargo-metrics-page';
import { KargoRoutesWrapper } from './feature/kargo/kargo-routes-wrapper';
import { KubeVisionPage } from './feature/kubevision/kubevision-page';
import { KubeVisionRoutesWrapper } from './feature/kubevision/kubevision-routes-wrapper';
import { KubeVisionSettingsPage } from './feature/kubevision/kubevision-settings-page';
import { KubeVisionUsagePage } from './feature/kubevision/kubevision-usage-page';
import { OrganizationSettingsPage } from './feature/organization/organization-settings/organization-settings-page';
import { NotificationCenter } from './feature/user/notifications/notification-center';
import { NotificationsSettings } from './feature/user/notifications/settings/notifications-settings';
import { Workspaces } from './feature/workspace//list/workspaces';
import { WorkspaceDetails } from './feature/workspace/workspace-details/workspace-details';
import { WorkspaceSettings } from './feature/workspace/workspace-settings/workspace-settings';
import { NotificationContextProvider } from './lib/context/notification-context';
import { ErrorFallbackPage } from './views/error-fallback-page';
import { NotFoundPage } from './views/not-found-page';

const LicensePage = React.lazy(() =>
  import('@ui/views/license/license-page').then((m) => ({ default: m.LicensePage }))
);

ChartJS.defaults.font.family = 'Satoshi-Variable';

ChartJS.register(
  ArcElement,
  Tooltip,
  Legend,
  CategoryScale,
  LinearScale,
  TimeScale,
  BarElement,
  Legend,
  PointElement,
  LineElement,
  zoomPlugin,
  TreemapController,
  TreemapElement
);

const sentryCreateBrowserRouter = Sentry.wrapCreateBrowserRouterV6(createBrowserRouter);

const router = sentryCreateBrowserRouter(
  createRoutesFromElements(
    <>
      <Route
        element={
          <Sentry.ErrorBoundary fallback={ErrorFallbackPage}>
            <Outlet />
          </Sentry.ErrorBoundary>
        }
      >
        <Route path={paths.awsOnboard} element={<MarketplaceOnboard name={'aws'} />} />
        <Route path={paths.azureOnboard} element={<MarketplaceOnboard name={'azure'} />} />
        <Route path={paths.gcpOnboard} element={<MarketplaceOnboard name={'gcp'} />} />

        <Route path={paths.authError} element={<ErrorPage />} />

        <Route element={<MainContextProvider />}>
          <Route element={<Layout />}>
            <Route path={paths.demo} element={<Demo />} />

            <Route path={paths.home} element={<Navigate to={paths.instances} replace={true} />} />
            <Route path={paths.account} element={<AccountPage />} />
            <Route path={paths.notifications} element={<NotificationCenter />} />
            <Route path={paths.notificationsSettings} element={<NotificationsSettings />} />

            <Route path={paths.instances} element={<InstancesListPage />} />
            <Route path={paths.newInstance} element={<NewInstancePage />} />
            <Route path={paths.instance} element={<InstanceDetailsPage />} />
            <Route path={paths.argoCDMetrics} element={<ArgoCDMetricsPage />} />
            <Route path={paths.clustersAddonsSummary} element={<ClustersAddonDetailsPage />} />
            <Route
              path={paths.clustersAddonsManifestSource}
              element={<ClustersAddonDetailsPage tab='manifestSource' />}
            />
            <Route
              path={paths.clustersAddonsCustomization}
              element={<ClustersAddonDetailsPage tab='clusterCustomization' />}
            />
            <Route path={paths.argoCDInstanceSettings} element={<ArgoCDSettings />} />

            <Route element={<KargoRoutesWrapper />}>
              <Route path={paths.kargoInstances} element={<KargoInstances />} />
              <Route path={paths.newKargoInstance} element={<CreateKargoInstance />} />
              <Route path={paths.kargoInstance} element={<KargoInstance />} />
              <Route path={paths.kargoInstanceSettings} element={<KargoInstanceSettings />} />
              <Route path={paths.kargoMetrics} element={<KargoMetricsPage />} />
            </Route>

            <Route element={<KubeVisionRoutesWrapper />}>
              <Route path={paths.kubevision} element={<KubeVisionPage />} />
              <Route path={paths.kubevisionUsage} element={<KubeVisionUsagePage />} />
              <Route path={paths.kubevisionSettings} element={<KubeVisionSettingsPage />} />
            </Route>

            <Route path={paths.organization} element={<OrganizationDetailsPage />} />
            <Route path={paths.organizationSettings} element={<OrganizationSettingsPage />} />
            <Route path={paths.joinOrganization} element={<OrganizationJoinPage />} />
            <Route path={paths.organizations} element={<OrganizationsSettingsPage />} />

            <Route path={paths.workspaces} element={<Workspaces />} />
            <Route path={paths.workspaceDetails} element={<WorkspaceDetails tab='instances' />} />
            <Route path={paths.workspaceSettings} element={<WorkspaceSettings />} />

            <Route
              path={paths.license}
              element={
                <React.Suspense fallback={<Loading />}>
                  <LicensePage />
                </React.Suspense>
              }
            />
          </Route>

          <Route path='*' element={<NotFoundPage />} />
        </Route>
      </Route>
    </>
  )
);

export const AppRouter = () => (
  // Double ConfigProvider - fix for direct Antd components import (https://github.com/akuityio/akuity-platform/issues/2122)
  <MainConfigProvider theme={theme} csp={{ nonce: __webpack_nonce__ }}>
    <ConfigProvider theme={theme} csp={{ nonce: __webpack_nonce__ }}>
      <NotificationContextProvider>
        <QueryClientProvider client={queryClient}>
          <RouterProvider router={router} />
        </QueryClientProvider>
      </NotificationContextProvider>
    </ConfigProvider>
  </MainConfigProvider>
);
