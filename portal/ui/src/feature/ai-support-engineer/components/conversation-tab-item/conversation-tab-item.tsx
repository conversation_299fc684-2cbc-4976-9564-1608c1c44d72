import { useQueryClient } from '@tanstack/react-query';
import { Flex } from 'antd';
import { useRef } from 'react';

import {
  useGetAIConversationQuery,
  useWatchAIConversation
} from '@ui/lib/apiclient/organization/ai-queries';
import {
  AIMessageContext,
  GetAIConversationResponse,
  Incident
} from '@ui/lib/apiclient/organization/v1/organization_pb';
import { queryKeys } from '@ui/lib/apiclient/query-keys';
import { Loading } from '@ui/lib/components/loading';
import { useAISupportEngineerContext } from '@ui/lib/context/ai-support-engineer-context';
import { smallObjectDeepCompare } from '@ui/lib/utils';

import { ConversationTabContent } from '../conversation-tab-content/conversation-tab-content';

type ConversationTabItemProps = {
  isSelected: boolean;
  conversationId: string;
  isHorizontalLayout: boolean;
  onSendMessage: (message: string, contexts: AIMessageContext[], runbooks: string[]) => void;
  onConversationUpdate: () => void;
  onConversationDelete: () => void;
};

export const ConversationTabItem = ({
  isSelected,
  conversationId,
  isHorizontalLayout,
  onSendMessage,
  onConversationUpdate,
  onConversationDelete
}: ConversationTabItemProps) => {
  const queryClient = useQueryClient();
  const { organizationId, instanceId } = useAISupportEngineerContext();
  const { data, isError } = useGetAIConversationQuery(conversationId);
  const { data: watchedData } = useWatchAIConversation(conversationId, {
    enabled: !!isSelected
  });
  const prevIncident = useRef<Incident | undefined>(undefined);
  const conversation = isSelected ? watchedData?.conversation : data?.conversation;
  const currentIncident = conversation?.incident;
  if (prevIncident.current && !smallObjectDeepCompare(prevIncident.current, currentIncident)) {
    onConversationUpdate();
  }
  prevIncident.current = currentIncident;

  if (!isError && !conversation) {
    return (
      <Flex justify='center' align='center' className='h-full'>
        <Loading />
      </Flex>
    );
  }

  if (isError) {
    return null;
  }

  return (
    <ConversationTabContent
      {...conversation}
      updateContexts={(contexts) =>
        queryClient.setQueryData(
          isSelected
            ? queryKeys.ai.watchConversation(organizationId, instanceId, conversationId).queryKey
            : queryKeys.ai.getConversation(organizationId, instanceId, conversationId).queryKey,
          () => {
            const old = isSelected ? watchedData : data;
            if (!old) return old;
            return new GetAIConversationResponse({
              ...old,
              conversation: {
                ...old.conversation,
                contexts
              }
            });
          }
        )
      }
      updateRunbooks={(runbooks) =>
        queryClient.setQueryData(
          isSelected
            ? queryKeys.ai.watchConversation(organizationId, instanceId, conversationId).queryKey
            : queryKeys.ai.getConversation(organizationId, instanceId, conversationId).queryKey,
          () => {
            const old = isSelected ? watchedData : data;
            if (!old) return old;
            return new GetAIConversationResponse({
              ...old,
              conversation: {
                ...old.conversation,
                runbooks
              }
            });
          }
        )
      }
      isHorizontalLayout={isHorizontalLayout}
      onSendMessage={(message) =>
        onSendMessage(message, conversation?.contexts || [], conversation?.runbooks || [])
      }
      onConversationUpdate={onConversationUpdate}
      onConversationDelete={onConversationDelete}
    />
  );
};
