import { PlainMessage } from '@bufbuild/protobuf';
import { keepPreviousData } from '@tanstack/react-query';
import { Button, ConfigProvider, Input } from 'antd';
import React, { useState } from 'react';

import { useListAIConversationSuggestionsQuery } from '@ui/lib/apiclient/organization/ai-queries';
import { AIMessageContext } from '@ui/lib/apiclient/organization/v1/organization_pb';
import { useAISupportEngineerContext } from '@ui/lib/context/ai-support-engineer-context';
import { useModal } from '@ui/lib/hooks';

import { ContextSelector } from '../context-selector/context-selector';
import { RunbookSelector } from '../runbook-selector/runbook-selector';
import { SuggestionsList } from '../suggestions-list/suggestions-list';

import { ConversationFeedbackModal } from './conversation-feedback-modal';

interface ConversationInputAreaProps {
  isHorizontal?: boolean;
  className?: string;
  style?: React.CSSProperties;
  onSendMessage?: (message: string, contexts: PlainMessage<AIMessageContext>[]) => void;
  isStreamingAnimationActive?: boolean;
  conversationId: string;
  contexts: PlainMessage<AIMessageContext>[];
  updateContexts: (contexts: PlainMessage<AIMessageContext>[]) => void;
  runbooks: string[];
  updateRunbooks: (runbooks: string[]) => void;
}

export const ConversationInputArea = ({
  isHorizontal = true,
  className = '',
  style = {},
  onSendMessage,
  conversationId,
  contexts,
  updateContexts,
  runbooks,
  updateRunbooks
}: ConversationInputAreaProps) => {
  const { instanceId, modelVersion } = useAISupportEngineerContext();
  const [inputValue, setInputValue] = useState('');
  const { show } = useModal();

  const finalIsDisabled = false; // TODO: Implement logic to determine if the input is disabled

  const handleSend = () => {
    if (inputValue.trim() && !finalIsDisabled) {
      onSendMessage?.(inputValue, contexts);
      setInputValue('');
    }
  };

  // Check if there are selected contexts
  const hasSelectedContexts = (contexts?.length ?? 0) > 0;

  // Only fetch suggestions if there are no user messages and there are selected contexts
  const { data, isFetching } = useListAIConversationSuggestionsQuery(
    {
      instanceId,
      conversationId,
      contexts
    },
    {
      enabled: hasSelectedContexts,
      placeholderData: keepPreviousData
    }
  );

  // Show suggestions during loading or when there are suggestions
  const showSuggestions = hasSelectedContexts && (isFetching || data?.suggestions?.length > 0);

  return (
    <div
      className={`input-area p-4 border border-slate-200 rounded-b-md ${className}`}
      style={style}
    >
      <ConfigProvider
        theme={{
          components: {
            Button: {
              colorText: '#64748b',
              colorPrimaryHover: '#475569',
              defaultBg: 'transparent',
              defaultHoverBg: 'rgba(243, 245, 247, 0.6)'
            }
          }
        }}
      >
        {/* Suggestions */}
        {showSuggestions && (
          <div className={isHorizontal ? '' : 'mb-2'}>
            <SuggestionsList
              suggestions={data?.suggestions || []}
              isLoading={isFetching}
              isDisabled={finalIsDisabled}
              onSuggestionClick={(prompt) => onSendMessage?.(prompt, contexts)}
            />
          </div>
        )}

        <div className='msg-container flex flex-col gap-2 rounded-2xl border border-solid border-gray-500 p-3'>
          {/* Context Selector */}
          <ContextSelector selectedContexts={contexts} updateContexts={updateContexts} />
          {/* Runbook Selector */}
          <RunbookSelector selectedRunbooks={runbooks} updateRunbooks={updateRunbooks} />

          <Input.TextArea
            placeholder='Type your message here...'
            className={isHorizontal ? 'msg-textarea' : 'flex-1 msg-textarea'}
            // autoSize={isHorizontal ? { minRows: 2, maxRows: 6 } : undefined}
            style={!isHorizontal ? { resize: 'none', minHeight: '150px' } : undefined}
            value={inputValue}
            disabled={finalIsDisabled}
            onChange={(e) => setInputValue(e.target.value)}
            onPressEnter={(e) => {
              // Prevent default behavior if shift key is pressed or if the input is in composition mode
              // is composition mode is important for asian languages
              if (!e.shiftKey && !e.nativeEvent.isComposing) {
                e.preventDefault();
                handleSend();
              }
            }}
            data-gramm='false'
          />

          <div className={`input-area-container ${!isHorizontal ? 'mt-4' : ''}`}>
            <span className='powered-by'>Powered by {modelVersion}</span>
            <div className='ms-auto flex flex-row items-center'>
              <Button
                type='text'
                onClick={() => {
                  show((props) => (
                    <ConversationFeedbackModal
                      conversationId={conversationId}
                      visible={props.visible}
                      hide={props.hide}
                    />
                  ));
                }}
              >
                Feedback
              </Button>
              <Button
                type='primary'
                className='send-button ms-auto'
                onClick={handleSend}
                disabled={finalIsDisabled}
              >
                Send
              </Button>
            </div>
          </div>
        </div>
      </ConfigProvider>
    </div>
  );
};
