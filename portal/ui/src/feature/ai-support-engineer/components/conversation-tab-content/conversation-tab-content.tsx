import { PlainMessage } from '@bufbuild/protobuf';
import { faEdit, faEye, faEyeSlash, faTrash } from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { Button, Flex, Input, Modal } from 'antd';
import { useCallback, useState } from 'react';

import {
  useDeleteAIConversationMutation,
  useUpdateAIConversationMutation,
  useUpdateIncidentMutation
} from '@ui/lib/apiclient/organization/ai-queries';
import {
  AIConversation,
  AIMessageContext,
  UpdateAIConversationRequest
} from '@ui/lib/apiclient/organization/v1/organization_pb';
import { useNotificationContext } from '@ui/lib/context/notification-context';
import { useModal } from '@ui/lib/hooks';

import { ConversationArea } from '../conversation-area/conversation-area';
import { ConversationInputArea } from '../conversation-input-area/conversation-input-area';

interface ConversationTabContentProps extends PlainMessage<AIConversation> {
  updateContexts: (contexts: PlainMessage<AIMessageContext>[]) => void;
  updateRunbooks: (runbooks: string[]) => void;
  isHorizontalLayout: boolean;
  onSendMessage: (message: string) => void;
  onConversationUpdate?: () => void;
  onConversationDelete?: () => void;
}

export const ConversationTabContent = ({
  isHorizontalLayout,
  onSendMessage,
  updateContexts,
  updateRunbooks,
  onConversationUpdate,
  onConversationDelete,
  ...conversation
}: ConversationTabContentProps) => {
  const updateConversationMutation = useUpdateAIConversationMutation();
  const deleteConversationMutation = useDeleteAIConversationMutation();
  const { mutateAsync: updateIncident } = useUpdateIncidentMutation();
  const [editingConversation, setEditingConversation] =
    useState<PlainMessage<AIConversation> | null>(null);
  const [editTitle, setEditTitle] = useState('');
  const notification = useNotificationContext();
  const { show } = useModal();

  const handleTogglePublic = useCallback(
    async (e: React.MouseEvent, chat: PlainMessage<AIConversation>) => {
      const chatTypeName = chat.incident ? 'Incident' : 'Conversation';
      e.stopPropagation();
      show((props) => (
        <Modal
          title={`Are you sure you want to ${chat.public ? 'unpublish' : 'publish'} this ${chatTypeName.toLowerCase()}?`}
          open={props.visible}
          onCancel={props.hide}
          onOk={async () => {
            try {
              await updateConversationMutation.mutateAsync({
                id: chat.id,
                title: chat.title,
                public: !chat.public,
                incident: chat.incident ? { resolved: !!chat.incident.resolvedAt } : null,
                contexts: chat.contexts
              });
              onConversationUpdate?.();
              props.hide();
              notification.success({
                message: `${chatTypeName} ${chat.public ? 'unpublished' : 'published'} successfully`,
                placement: 'bottomRight'
              });
            } catch (error) {
              notification.error({
                message: `Failed to update ${chatTypeName}`,
                placement: 'bottomRight'
              });
            }
          }}
        >
          <p>
            {chat.public
              ? `This will make the ${chatTypeName.toLowerCase()} private and remove it from public view.`
              : `This will make the ${chatTypeName.toLowerCase()} public and visible to others.`}
          </p>
        </Modal>
      ));
    },
    []
  );

  const handleEditTitle = useCallback(
    async (e: React.MouseEvent, chat: PlainMessage<AIConversation>) => {
      e.stopPropagation();
      setEditTitle(chat.title);
      setEditingConversation(chat);
    },
    [editTitle, setEditTitle, show]
  );

  const handleDelete = useCallback(
    async (e: React.MouseEvent, chat: PlainMessage<AIConversation>) => {
      const chatTypeName = chat.incident ? 'Incident' : 'Conversation';
      e.stopPropagation();
      show((props) => (
        <Modal
          title={`Are you sure you want to delete this ${chatTypeName.toLowerCase()}?`}
          open={props.visible}
          onCancel={props.hide}
          onOk={async () => {
            try {
              await deleteConversationMutation.mutateAsync({
                id: chat.id
              });
              onConversationDelete?.();
              props.hide();
              notification.success({
                message: `${chatTypeName} deleted successfully`,
                placement: 'bottomRight'
              });
            } catch (error) {
              notification.error({
                message: `Failed to delete ${chatTypeName}`,
                placement: 'bottomRight'
              });
            }
          }}
        >
          <p>
            Are you sure you want to delete this{' '}
            {conversation?.incident ? 'incident' : 'conversation'}? This action cannot be undone.
          </p>
        </Modal>
      ));
    },
    []
  );

  return (
    <>
      <Flex
        vertical
        className='h-[calc(100vh-150px)] rounded-md rounded-ss-none overflow-hidden tab-content'
      >
        <Flex align='center' justify='between' className='p-2 w-full'>
          {conversation?.incident && (
            <Button
              type='primary'
              onClick={() => {
                const isResolved = !!conversation.incident.resolvedAt;
                show((props) => (
                  <Modal
                    title={isResolved ? 'Mark as Unresolved' : 'Mark as Resolved'}
                    open={props.visible}
                    onOk={async () => {
                      await updateIncident(
                        UpdateAIConversationRequest.fromJson({
                          id: conversation?.id,
                          instanceId: conversation?.instanceId,
                          title: conversation?.title,
                          public: conversation?.public,
                          contexts: conversation?.contexts,
                          incident: {
                            resolved: !isResolved
                          }
                        })
                      );
                      onConversationUpdate?.();
                      props.hide();
                    }}
                    onCancel={props.hide}
                  >
                    <p>Mark this incident as {isResolved ? 'unresolved' : 'resolved'}?</p>
                  </Modal>
                ));
              }}
            >
              Mark as {conversation.incident.resolvedAt ? 'Unresolved' : 'Resolved'}
            </Button>
          )}
          <Flex justify='end' gap={4}>
            <Button
              type='text'
              icon={
                <FontAwesomeIcon
                  icon={conversation.public ? faEye : faEyeSlash}
                  className='text-gray-400 hover:text-gray-600'
                />
              }
              className='p-1'
              onClick={(e) => handleTogglePublic(e, conversation)}
            />
            <Button
              type='text'
              icon={<FontAwesomeIcon icon={faEdit} className='text-gray-400 hover:text-gray-600' />}
              className='p-1'
              onClick={(e) => handleEditTitle(e, conversation)}
            />
            <Button
              type='text'
              icon={<FontAwesomeIcon icon={faTrash} className='text-gray-400 hover:text-red-500' />}
              className='p-1'
              onClick={(e) => handleDelete(e, conversation)}
            />
          </Flex>
        </Flex>
        <Flex vertical={!isHorizontalLayout} className='flex-1 min-h-0 overflow-hidden'>
          {isHorizontalLayout ? (
            <Flex className='flex-1 overflow-hidden'>
              <div className='flex-1 min-w-0'>
                <ConversationArea
                  {...conversation}
                  updateContexts={updateContexts}
                  onSendMessage={onSendMessage}
                  className=' h-full'
                />
              </div>
              <div className='flex-1 min-w-0'>
                <ConversationInputArea
                  conversationId={conversation.id}
                  contexts={conversation.contexts}
                  runbooks={conversation.runbooks}
                  updateContexts={updateContexts}
                  updateRunbooks={updateRunbooks}
                  isHorizontal={false}
                  className='h-full'
                  onSendMessage={onSendMessage}
                />
              </div>
            </Flex>
          ) : (
            <>
              <ConversationArea
                {...conversation}
                updateContexts={updateContexts}
                onSendMessage={onSendMessage}
              />
              <ConversationInputArea
                conversationId={conversation.id}
                contexts={conversation.contexts}
                runbooks={conversation.runbooks}
                updateContexts={updateContexts}
                updateRunbooks={updateRunbooks}
                isHorizontal={true}
                onSendMessage={onSendMessage}
              />
            </>
          )}
        </Flex>
      </Flex>

      <Modal
        title={`Edit ${editingConversation?.incident ? 'Incident' : 'Conversation'} Title`}
        open={!!editingConversation}
        onCancel={() => setEditingConversation(null)}
        onOk={async () => {
          if (!editingConversation) return;
          const chatTypeName = editingConversation.incident ? 'Incident' : 'Conversation';
          try {
            await updateConversationMutation.mutateAsync({
              id: editingConversation.id,
              title: editTitle,
              public: editingConversation.public,
              incident: editingConversation.incident
                ? { resolved: !!editingConversation.incident.resolvedAt }
                : null,
              contexts: editingConversation.contexts
            });
            onConversationUpdate?.();
            setEditingConversation(null);
            notification.success({
              message: `${chatTypeName} title updated successfully`,
              placement: 'bottomRight'
            });
          } catch (error) {
            notification.error({
              message: `Failed to update ${chatTypeName.toLowerCase()} title`,
              placement: 'bottomRight'
            });
          }
        }}
      >
        <Input value={editTitle} onChange={(e) => setEditTitle(e.target.value)} />
      </Modal>
    </>
  );
};
