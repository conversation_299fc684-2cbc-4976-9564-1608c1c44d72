import { PlainMessage } from '@bufbuild/protobuf';
import { faGear, faPlus, faRemove } from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { zodResolver } from '@hookform/resolvers/zod';
import { Button, Col, Empty, Input, Modal, Row, Select, Space, Table } from 'antd';
import { useForm, UseFormReturn } from 'react-hook-form';
import { z } from 'zod';

import { Cluster, Instance } from '@ui/lib/apiclient';
import {
  CustomDeprecatedAPI,
  TargetSelector,
  IncidentWebhookConfig,
  KubeVisionConfig
} from '@ui/lib/apiclient/argocd/v1/argocd_pb';
import { FormSection } from '@ui/lib/components/forms';
import { FieldContainer } from '@ui/lib/components/forms/field-container';
import { useMainContext } from '@ui/lib/context/main-context';
import { ModalComponent } from '@ui/lib/context/modal-context';
import { ConfirmProps } from '@ui/lib/hooks/use-confirm-modal';

const TriggerModal = ({
  visible,
  hide,
  index = -1,
  triggers,
  setTriggers
}: {
  visible: boolean;
  hide: () => void;
  triggers: PlainMessage<TargetSelector>[];
  setTriggers?: (triggers: PlainMessage<TargetSelector>[]) => void;
  index?: number;
}) => {
  const trigger = triggers[index] || {
    argocdApplications: [],
    k8sNamespaces: [],
    clusters: []
  };
  const form = useForm({
    defaultValues: trigger,
    resolver: zodResolver(
      z.object({
        argocdApplications: z.array(z.string()).optional(),
        k8sNamespaces: z.array(z.string()).optional(),
        clusters: z.array(z.string()).optional(),
        autoCreateIncident: z.boolean().optional()
      })
    )
  });
  const submit = form.handleSubmit(async (trigger) => {
    setTriggers?.(
      index === -1
        ? triggers.concat(trigger)
        : [...triggers.slice(0, index), trigger, ...triggers.slice(index + 1)]
    );
    form.reset();
  });

  return (
    <Modal
      title={index > -1 ? 'Update Trigger' : 'New Trigger'}
      open={visible}
      onCancel={hide}
      onOk={() => {
        submit().then(hide);
      }}
    >
      <FieldContainer control={form.control} name='argocdApplications' label='Argo CD Applications'>
        {({ field: { value, onChange } }) => (
          <Select value={value} onChange={onChange} className='w-full' mode='tags' />
        )}
      </FieldContainer>
      <FieldContainer control={form.control} name='k8sNamespaces' label='K8S Namespaces'>
        {({ field: { value, onChange } }) => (
          <Select value={value} onChange={onChange} className='w-full' mode='tags' />
        )}
      </FieldContainer>
      <FieldContainer control={form.control} name='clusters' label='Clusters'>
        {({ field: { value, onChange } }) => (
          <Select value={value} onChange={onChange} className='w-full' mode='tags' />
        )}
      </FieldContainer>
    </Modal>
  );
};

const WebhookConfigModal = ({
  visible,
  hide,
  index = -1,
  webhooks,
  setWebhooks
}: {
  visible: boolean;
  hide: () => void;
  webhooks: PlainMessage<IncidentWebhookConfig>[];
  setWebhooks?: (webhooks: PlainMessage<IncidentWebhookConfig>[]) => void;
  index?: number;
}) => {
  const webhook = webhooks[index] || {
    name: '',
    descriptionPath: '',
    clusterPath: '',
    k8sNamespacePath: '',
    argocdApplicationNamePath: ''
  };
  const form = useForm({
    defaultValues: webhook,
    resolver: zodResolver(
      z.object({
        name: z.string().min(1),
        descriptionPath: z.string(),
        clusterPath: z.string(),
        k8sNamespacePath: z.string(),
        argocdApplicationNamePath: z.string()
      })
    )
  });
  const submit = form.handleSubmit(async (config) => {
    setWebhooks?.(
      index === -1
        ? webhooks.concat(config)
        : [...webhooks.slice(0, index), config, ...webhooks.slice(index + 1)]
    );
    form.reset();
    hide();
  });

  return (
    <Modal
      title={index > -1 ? 'Update Webhook Config' : 'New Webhook Config'}
      open={visible}
      onCancel={hide}
      onOk={() => submit()}
    >
      <FieldContainer control={form.control} name='name' label='Name'>
        {({ field: { value, onChange } }) => (
          <Input value={value} onChange={onChange} className='w-full' placeholder='alert-manager' />
        )}
      </FieldContainer>
      <FieldContainer control={form.control} name='descriptionPath' label='Description'>
        {({ field: { value, onChange } }) => (
          <Input
            value={value}
            onChange={onChange}
            className='w-full'
            placeholder='{.body.alerts[0].annotations.description}'
          />
        )}
      </FieldContainer>
      <FieldContainer control={form.control} name='clusterPath' label='Cluster'>
        {({ field: { value, onChange } }) => (
          <Input
            value={value}
            onChange={onChange}
            className='w-full'
            placeholder='{.query.clusterName}'
          />
        )}
      </FieldContainer>
      <FieldContainer control={form.control} name='k8sNamespacePath' label='K8S Namespace'>
        {({ field: { value, onChange } }) => (
          <Input
            value={value}
            onChange={onChange}
            className='w-full'
            placeholder='{.body.alerts[0].labels.namespace}'
          />
        )}
      </FieldContainer>
      <FieldContainer
        control={form.control}
        name='argocdApplicationNamePath'
        label='Argo CD Application Name'
      >
        {({ field: { value, onChange } }) => (
          <Input value={value} onChange={onChange} className='w-full' />
        )}
      </FieldContainer>
    </Modal>
  );
};

export const IncidentsTab = ({
  form,
  show,
  instance,
  confirm
}: {
  form: UseFormReturn<{
    multiClusterK8sDashboardEnabled: boolean;
    kubeVisionConfig: PlainMessage<KubeVisionConfig>;
    customDeprecatedApis: CustomDeprecatedAPI[];
    enableForAllClusters: boolean;
    clusterUpdates: Record<string, Cluster>;
  }>;
  show: (newComponent?: ModalComponent) => void;
  instance: Instance;
  confirm: (propsConfirmModal: ConfirmProps) => void;
}) => {
  const ctx = useMainContext();
  const { watch, setValue } = form;

  const formValues = watch();

  return (
    <>
      <FormSection
        title='Resource Degradation Triggers'
        className='pb-4 mb-3'
        docsText='Resource Degradation Triggers automatically create incidents when resources are degraded.'
      >
        <Row className='w-full' align='middle'>
          <Col span={12}>
            <div className='text-sm text-gray-500 mb-4'>
              Configure which degraded objects will trigger an incident.
            </div>
            <Table
              className='mt-2'
              columns={[
                {
                  key: 'argocdApplications',
                  title: 'Argo CD Applications',
                  render: (
                    _: PlainMessage<TargetSelector>[],
                    item: PlainMessage<TargetSelector>
                  ) => (
                    <>
                      {item.argocdApplications?.length > 0
                        ? item.argocdApplications.join(', ')
                        : 'none'}
                    </>
                  ),
                  width: '20%'
                },
                {
                  key: 'k8sNamespaces',
                  title: 'K8S Namespaces',
                  render: (
                    _: PlainMessage<TargetSelector>[],
                    item: PlainMessage<TargetSelector>
                  ) => (
                    <>{item.k8sNamespaces?.length > 0 ? item.k8sNamespaces.join(', ') : 'none'}</>
                  ),
                  width: '20%'
                },
                {
                  key: 'clusters',
                  title: 'Clusters',
                  render: (
                    _: PlainMessage<TargetSelector>[],
                    item: PlainMessage<TargetSelector>
                  ) => <>{item.clusters?.length > 0 ? item.clusters.join(', ') : 'any'}</>,
                  width: '20%'
                },
                {
                  key: 'action',
                  width: '10%',
                  render: (
                    _: PlainMessage<TargetSelector>[],
                    item: PlainMessage<TargetSelector>,
                    index: number
                  ) => (
                    <Space>
                      <Button
                        icon={<FontAwesomeIcon icon={faGear} />}
                        onClick={() =>
                          show((props) => (
                            <TriggerModal
                              {...props}
                              triggers={
                                formValues.kubeVisionConfig?.aiConfig?.incidents?.triggers || []
                              }
                              setTriggers={(configs) =>
                                setValue('kubeVisionConfig.aiConfig.incidents.triggers', configs)
                              }
                              index={index}
                            />
                          ))
                        }
                      />
                      <Button
                        icon={<FontAwesomeIcon icon={faRemove} />}
                        type='text'
                        onClick={() =>
                          confirm({
                            title: 'Are you sure you want to remove trigger?',
                            onOk: () =>
                              setValue('kubeVisionConfig.aiConfig.incidents.triggers', [
                                ...formValues.kubeVisionConfig.aiConfig.incidents.triggers.slice(
                                  0,
                                  index
                                ),
                                ...formValues.kubeVisionConfig.aiConfig.incidents.triggers.slice(
                                  index + 1
                                )
                              ])
                          })
                        }
                      />
                    </Space>
                  )
                }
              ]}
              dataSource={formValues.kubeVisionConfig?.aiConfig?.incidents?.triggers?.map(
                (item, i) => ({
                  ...item,
                  key: i
                })
              )}
              locale={{
                emptyText: <Empty description='No incident triggers configured' className='py-4' />
              }}
            />
            <Button
              onClick={() => {
                show((props) => (
                  <TriggerModal
                    {...props}
                    triggers={formValues.kubeVisionConfig?.aiConfig?.incidents?.triggers || []}
                    setTriggers={(triggers) =>
                      setValue('kubeVisionConfig.aiConfig.incidents.triggers', triggers)
                    }
                  />
                ));
              }}
              className='mt-2'
              type='primary'
              icon={<FontAwesomeIcon icon={faPlus} />}
            >
              Add New
            </Button>
          </Col>
        </Row>
      </FormSection>
      <FormSection
        title='Webhook Triggers'
        className='pb-4 mb-3'
        docsText='Webhook triggers are used to create incidents based on external events.'
      >
        <div className='text-sm text-gray-500 mb-4'>
          Configure your alerting system to send incidents to Akuity Intelligence.
        </div>
        <Row className='w-full' align='middle'>
          <Col span={12}>
            <Table
              className='mt-2'
              columns={[
                {
                  key: 'name',
                  title: 'Webhook',
                  render: (
                    _: PlainMessage<IncidentWebhookConfig>[],
                    item: PlainMessage<IncidentWebhookConfig>
                  ) => {
                    const port = window.location.port ? `:${window.location.port}` : '';
                    const url = `${window.location.protocol}//${window.location.hostname}${port}/api/v1/orgs/${ctx.currentOrg?.id}/ai/incidents?webhook_name=${item.name}&instance_id=${instance.id}`;
                    return (
                      <>
                        <div>
                          <strong>Name:</strong> {item.name}
                        </div>
                        <div>
                          <strong>URL:</strong> <a href={url}>{url}</a>
                        </div>
                      </>
                    );
                  },
                  width: '60%'
                },
                {
                  key: 'fieldPaths',
                  title: 'Fields Paths',
                  render: (
                    _: PlainMessage<IncidentWebhookConfig>[],
                    item: PlainMessage<IncidentWebhookConfig>
                  ) => (
                    <>
                      {item.descriptionPath && (
                        <div className='whitespace-nowrap overflow-hidden text-ellipsis'>
                          <strong>Description:</strong> {item.descriptionPath}
                        </div>
                      )}
                      {item.k8sNamespacePath && (
                        <div className='whitespace-nowrap overflow-hidden text-ellipsis'>
                          <strong>K8S Namespace:</strong> {item.k8sNamespacePath}
                        </div>
                      )}
                      {item.clusterPath && (
                        <div className='whitespace-nowrap overflow-hidden text-ellipsis'>
                          <strong>Cluster:</strong> {item.clusterPath}
                        </div>
                      )}
                      {item.argocdApplicationNamePath && (
                        <div className='whitespace-nowrap overflow-hidden text-ellipsis'>
                          <strong>Application Name:</strong> {item.argocdApplicationNamePath}
                        </div>
                      )}
                    </>
                  ),
                  width: '20%'
                },
                {
                  key: 'action',
                  render: (
                    _: PlainMessage<IncidentWebhookConfig>[],
                    item: PlainMessage<IncidentWebhookConfig>,
                    index: number
                  ) => (
                    <Space>
                      <Button
                        icon={<FontAwesomeIcon icon={faGear} />}
                        type='text'
                        onClick={() =>
                          show((props) => (
                            <WebhookConfigModal
                              {...props}
                              webhooks={
                                formValues.kubeVisionConfig?.aiConfig?.incidents?.webhooks || []
                              }
                              setWebhooks={(configs) =>
                                setValue('kubeVisionConfig.aiConfig.incidents.webhooks', configs)
                              }
                              index={index}
                            />
                          ))
                        }
                      />
                      <Button
                        icon={<FontAwesomeIcon icon={faRemove} />}
                        type='text'
                        onClick={() =>
                          confirm({
                            title: 'Are you sure you want to remove webhook?',
                            onOk: () =>
                              setValue('kubeVisionConfig.aiConfig.incidents.webhooks', [
                                ...formValues.kubeVisionConfig.aiConfig.incidents.webhooks.slice(
                                  0,
                                  index
                                ),
                                ...formValues.kubeVisionConfig.aiConfig.incidents.webhooks.slice(
                                  index + 1
                                )
                              ])
                          })
                        }
                      />
                    </Space>
                  )
                }
              ]}
              dataSource={formValues.kubeVisionConfig?.aiConfig?.incidents?.webhooks?.map(
                (item, i) => ({
                  ...item,
                  key: i
                })
              )}
              locale={{
                emptyText: <Empty description='No incident webhooks configured' className='py-4' />
              }}
            />
            <Button
              onClick={() => {
                show((props) => (
                  <WebhookConfigModal
                    {...props}
                    webhooks={formValues.kubeVisionConfig?.aiConfig?.incidents?.webhooks || []}
                    setWebhooks={(configs) =>
                      setValue('kubeVisionConfig.aiConfig.incidents.webhooks', configs)
                    }
                  />
                ));
              }}
              className='mt-2'
              type='primary'
              icon={<FontAwesomeIcon icon={faPlus} />}
            >
              Add New
            </Button>
          </Col>
        </Row>
      </FormSection>
    </>
  );
};
