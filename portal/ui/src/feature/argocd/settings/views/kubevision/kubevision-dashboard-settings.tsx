import { PlainMessage } from '@bufbuild/protobuf';
import { faPlus, faTrash } from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { useMutation, useQuery } from '@tanstack/react-query';
import {
  Alert,
  Button,
  Col,
  Empty,
  Flex,
  Input,
  Row,
  Select,
  Switch,
  Table,
  Tabs,
  Tag
} from 'antd';
import { useState } from 'react';
import { useForm, Controller } from 'react-hook-form';
import { generatePath, useNavigate } from 'react-router-dom';

import { paths } from '@ui/config/paths';
import { SectionHeader } from '@ui/feature/argocd/settings/section-header';
import {
  isMinimumAgentVersionSatisfied,
  isMinimumAgentVersionSatisfiedForCveScan,
  isMinimumInstanceVersionSatisfied,
  MINIMUM_AGENT_VERSION_FOR_CVE_SCAN,
  MINIMUM_ARGENT_VERSION,
  MINIMUM_ARGO_VERSION
} from '@ui/feature/kubevision/version';
import { AkuityApiClient, Cluster, Instance, InstanceSpec } from '@ui/lib/apiclient';
import {
  ClusterKubernetesStatus,
  CustomDeprecatedAPI,
  KubeVisionConfig
} from '@ui/lib/apiclient/argocd/v1/argocd_pb';
import { FeatureStatus } from '@ui/lib/apiclient/types/features/v1/features_pb';
import { useMainContext } from '@ui/lib/context/main-context';
import { useNotificationContext } from '@ui/lib/context/notification-context';
import { useModal } from '@ui/lib/hooks';
import { useConfirmModal } from '@ui/lib/hooks/use-confirm-modal';
import { services } from '@ui/lib/services';
import { Subset } from '@ui/lib/types';
import { deepArrayCompare, errorToString, smallObjectDeepCompare } from '@ui/lib/utils';

import { useBlockSettingsNavigation } from '../../utils/use-block-settings-navigation';
import {
  addSyncerToCustomization,
  deleteSyncerFromCustomization,
  getCustomAgentSize
} from '../cluster-customization/agent-size-customization-field/utils';

import { IncidentsTab } from './incidents-tab';

export interface KubeVisionDashboardSettingsProps {
  instance: Instance;
  instanceName?: () => React.ReactNode;
  onSave?: () => void;
}

interface FormValues {
  multiClusterK8sDashboardEnabled: boolean;
  enableForAllClusters: boolean;
  clusterUpdates: Record<string, Cluster>;
  customDeprecatedApis: CustomDeprecatedAPI[];
  kubeVisionConfig: PlainMessage<KubeVisionConfig>;
}

export const KubeVisionDashboardSettings = ({
  instance,
  instanceName,
  onSave
}: KubeVisionDashboardSettingsProps) => {
  const notification = useNotificationContext();
  const { currentOrg, currentOrgFeatureStatuses } = useMainContext();
  const confirm = useConfirmModal();
  const navigate = useNavigate();

  const client = new AkuityApiClient();

  const form = useForm<FormValues>({
    defaultValues: {
      multiClusterK8sDashboardEnabled: instance.spec.multiClusterK8sDashboardEnabled,
      enableForAllClusters: false,
      clusterUpdates: {},
      customDeprecatedApis: instance.spec.customDeprecatedApis || [],
      kubeVisionConfig: instance.spec.kubeVisionConfig
        ? {
            cveScanConfig: {
              scanEnabled: instance.spec.kubeVisionConfig.cveScanConfig?.scanEnabled || false,
              rescanInterval: instance.spec.kubeVisionConfig.cveScanConfig?.rescanInterval || '8h'
            },
            aiConfig: {
              incidents: instance.spec.kubeVisionConfig.aiConfig?.incidents || {}
            }
          }
        : {
            cveScanConfig: {
              scanEnabled: false,
              rescanInterval: '8h'
            },
            aiConfig: {
              incidents: {}
            }
          }
    }
  });
  const { control, handleSubmit, reset, watch, setValue } = form;

  const formValues = watch();
  const { show } = useModal();

  const [showAlert, setShowAlert] = useState(false);

  const kubevisionDashboardUpdate = useMutation({
    mutationFn: async (formData: FormValues) => {
      try {
        const updates: Subset<InstanceSpec> = {};

        if (
          instance.spec.multiClusterK8sDashboardEnabled !== formData.multiClusterK8sDashboardEnabled
        ) {
          updates.multiClusterK8sDashboardEnabled = formData.multiClusterK8sDashboardEnabled;
        }

        // Initialize kubeVisionConfig for comparison
        const currentKubeVisionConfig = instance.spec.kubeVisionConfig || {
          cveScanConfig: { scanEnabled: false, rescanInterval: '8h' },
          aiConfig: { incidents: {} }
        };

        if (!smallObjectDeepCompare(currentKubeVisionConfig, formData.kubeVisionConfig)) {
          updates.kubeVisionConfig = formData.kubeVisionConfig;
        }

        if (
          !smallObjectDeepCompare(
            instance.spec.customDeprecatedApis || [],
            formData.customDeprecatedApis
          )
        ) {
          updates.customDeprecatedApis = formData.customDeprecatedApis;
        }

        if (Object.keys(updates).length > 0) {
          await client.instances.patch(currentOrg.id, instance.id, {
            spec: updates
          });
        }

        if (formData.enableForAllClusters) {
          await mutateEnableForAllClusters();
        }

        // Apply cluster updates if any exist
        const clusterUpdates = Object.entries(formData.clusterUpdates || {});
        if (clusterUpdates.length > 0) {
          for (const [, cluster] of clusterUpdates) {
            const newCluster = { ...cluster };

            const customAgentSize = getCustomAgentSize(newCluster.data.kustomization);
            if (customAgentSize.isCustomSize) {
              if (newCluster.data.multiClusterK8sDashboardEnabled) {
                newCluster.data.kustomization = addSyncerToCustomization(
                  newCluster.data.kustomization
                );
              } else {
                newCluster.data.kustomization = deleteSyncerFromCustomization(
                  newCluster.data.kustomization
                );
              }
            }
            await mutateClusterK8sDashboardFeature(newCluster);
          }
        }

        refetchClusters();

        // Reset clusterUpdates after saving
        setValue('clusterUpdates', {});
        setValue('enableForAllClusters', false);
        onSave?.();

        notification.success({
          message: 'Settings saved successfully'
        });
      } catch (err) {
        notification.error({
          message: errorToString(err)
        });
      }
    }
  });

  const { mutateAsync: mutateClusterK8sDashboardFeature } = useMutation({
    mutationFn: (cluster: Cluster) => {
      return services.apiClient.instances.updateCluster(
        currentOrg.id,
        instance.id,
        cluster.id,
        cluster
      );
    }
  });

  const [clusterQueryFilter, setClusterQueryFilter] = useState({ limit: 10, offset: 0 });
  const clustersQueryKey = [
    'organizations',
    currentOrg.id,
    'instances',
    instance.id,
    'clusters',
    clusterQueryFilter,
    formValues.multiClusterK8sDashboardEnabled
  ];

  const {
    data: clusterList,
    isPending: isLoadingClusters,
    refetch: refetchClusters
  } = useQuery({
    queryKey: clustersQueryKey,
    queryFn: () =>
      services.apiClient.instances.getClusters(currentOrg.id, instance.id, {
        limit: `${clusterQueryFilter.limit}`,
        offset: `${clusterQueryFilter.offset}`,
        excludeDirectCluster: true
      })
  });

  const { mutate: mutateEnableForAllClusters } = useMutation({
    mutationFn: () => {
      return services.apiClient.instances.updateClusters(currentOrg.id, instance.id, null, true);
    }
  });

  const handleEnableForAllClustersClicked = () => {
    // Create updates for all clusters
    const updates = { ...formValues.clusterUpdates };
    clusterList?.clusters.forEach((cluster) => {
      updates[cluster.id] = { ...cluster };
      updates[cluster.id].data.multiClusterK8sDashboardEnabled = true;
    });
    Object.keys(updates).forEach((key) => {
      updates[key].data.multiClusterK8sDashboardEnabled = true;
    });
    setValue('clusterUpdates', updates);
    setValue('enableForAllClusters', true);
  };

  const showInstanceVersionWarning = !isMinimumInstanceVersionSatisfied(instance?.version);
  const handleClickUpdateNow = () => {
    navigate(
      generatePath(paths.argoCDInstanceSettings, { org: currentOrg.name, name: instance.name })
    );
  };

  // Calculate changes for useBlockSettingsNavigation
  const changes = [];

  if (
    instance.spec.multiClusterK8sDashboardEnabled !== formValues.multiClusterK8sDashboardEnabled
  ) {
    changes.push('Akuity Intelligence Enabled Status');
  }

  const cveScanConfig = instance.spec.kubeVisionConfig?.cveScanConfig ?? {};
  cveScanConfig.rescanInterval = cveScanConfig.rescanInterval || '8h';
  cveScanConfig.scanEnabled = cveScanConfig.scanEnabled || false;
  if (!smallObjectDeepCompare(cveScanConfig, formValues.kubeVisionConfig?.cveScanConfig)) {
    changes.push('CVE Scan Configuration');
  }

  if (
    !smallObjectDeepCompare(
      instance.spec.customDeprecatedApis || [],
      formValues.customDeprecatedApis
    )
  ) {
    changes.push('Custom Deprecated APIs');
  }

  if (formValues.enableForAllClusters) {
    changes.push('Enable Akuity Intelligence for all clusters');
  }

  // Check if there are cluster updates
  if (formValues.clusterUpdates && Object.keys(formValues.clusterUpdates).length > 0) {
    changes.push('Cluster Akuity Intelligence Settings');
  }

  // Check if incidents config has changed (excluding triggers)
  const originalIncidents = instance.spec.kubeVisionConfig?.aiConfig?.incidents;
  const updatedIncidents = formValues.kubeVisionConfig?.aiConfig?.incidents;

  // Extract triggers and create objects without triggers for comparison
  const originalTriggers = originalIncidents?.triggers || [];
  const updatedTriggers = updatedIncidents?.triggers || [];

  const originalIncidentsWithoutTriggers = originalIncidents
    ? {
        webhooks: originalIncidents.webhooks
      }
    : {};

  const updatedIncidentsWithoutTriggers = updatedIncidents
    ? {
        webhooks: updatedIncidents.webhooks
      }
    : {};

  if (!smallObjectDeepCompare(originalIncidentsWithoutTriggers, updatedIncidentsWithoutTriggers)) {
    changes.push('Incident Detection Settings');
  }

  // Check if triggers have changed
  if (!deepArrayCompare(originalTriggers, updatedTriggers)) {
    changes.push('Incident Triggers');
  }

  useBlockSettingsNavigation(!!changes.length, reset, changes, 'Akuity Intelligence');

  const columns = [
    {
      key: 'name',
      title: 'Name',
      dataIndex: 'name',
      width: 200
    },
    {
      key: 'agentVersion',
      title: 'Installed Agent Version',
      width: 200,
      // append v to the version number
      render: (name: string, cluster: Cluster) => `v${cluster?.data?.targetVersion ?? ''}`
    },
    {
      key: 'k8sStatus',
      title: 'Status',
      dataIndex: 'k8sStatus',
      width: 320,
      render: (k8sStatus: ClusterKubernetesStatus, cluster: Cluster) => {
        const isEnabled =
          formValues.multiClusterK8sDashboardEnabled &&
          cluster.data.multiClusterK8sDashboardEnabled;
        if (!isEnabled || !k8sStatus.kubernetesVersion) {
          return <div>N/A</div>;
        }

        return (
          <>
            <Row style={{ width: '320px' }}>
              <Col xs={12}>Kubernetes Version:</Col>
              <Col xs={12}>{k8sStatus.kubernetesVersion}</Col>
            </Row>
            <Row style={{ width: '320px' }}>
              <Col xs={12}>API Resource:</Col>
              <Col xs={12}>{k8sStatus.apiResourceCount}</Col>
            </Row>
            <Row style={{ width: '320px' }}>
              <Col xs={12}>Object:</Col>
              <Col xs={12}>{k8sStatus.objectCount}</Col>
            </Row>
          </>
        );
      }
    },
    {
      key: 'monitoring',
      title: 'Akuity Intelligence',
      render: (_: string, cluster: Cluster) => {
        const isDisabled = !formValues.multiClusterK8sDashboardEnabled;
        const currentEnabled =
          formValues.clusterUpdates[cluster.id] !== undefined
            ? formValues.clusterUpdates[cluster.id].data.multiClusterK8sDashboardEnabled
            : formValues.enableForAllClusters || cluster.data.multiClusterK8sDashboardEnabled;
        const isEnabled = !isDisabled && currentEnabled;
        const showClusterVersionWarning = !isMinimumAgentVersionSatisfied(
          cluster?.data?.targetVersion ?? ''
        );
        const showCveScanWarning =
          formValues.kubeVisionConfig?.cveScanConfig?.scanEnabled &&
          !isMinimumAgentVersionSatisfiedForCveScan(cluster?.data?.targetVersion ?? '');
        return (
          <div className='flex'>
            <div style={{ width: '80px' }}>
              <b>{isEnabled ? 'Enabled' : 'Disabled'}</b>
            </div>
            <Switch
              checked={isEnabled}
              disabled={isDisabled}
              onChange={async () => {
                const currentValue =
                  formValues.clusterUpdates[cluster.id] !== undefined
                    ? formValues.clusterUpdates[cluster.id]
                    : cluster.data.multiClusterK8sDashboardEnabled;

                setValue('clusterUpdates', {
                  ...formValues.clusterUpdates,
                  [cluster.id]: {
                    ...cluster,
                    data: { ...cluster.data, multiClusterK8sDashboardEnabled: !currentValue }
                  }
                });
              }}
            />
            <div className={'ml-2'}>
              {showClusterVersionWarning && !showCveScanWarning && (
                <Tag color={'red'}>
                  Agent <b>v{MINIMUM_ARGENT_VERSION}</b> or higher is required!
                </Tag>
              )}
              {showCveScanWarning && (
                <Tag color={'red'}>
                  Agent <b>v{MINIMUM_AGENT_VERSION_FOR_CVE_SCAN}</b> or higher is required for CVE
                  Scan!
                </Tag>
              )}
            </div>
          </div>
        );
      }
    }
  ];

  const tabs = [
    {
      key: 'clusters',
      label: 'Clusters',
      children: (
        <>
          <Row justify='space-between'>
            <h2 className='font-bold mb-2 text-2xl'>Clusters</h2>
            <Button type='primary' onClick={handleEnableForAllClustersClicked}>
              Enable for all clusters
            </Button>
          </Row>
          <Table
            className='mt-2'
            columns={columns}
            dataSource={clusterList?.clusters.map((item) => ({ ...item, key: item.id }))}
            loading={kubevisionDashboardUpdate.isPending || isLoadingClusters}
            locale={{ emptyText: <Empty description='No clusters found' className='py-4' /> }}
            pagination={{
              pageSize: clusterQueryFilter.limit,
              total: Number.parseInt(clusterList?.totalCount ?? '0'),
              current: clusterQueryFilter.offset / +clusterQueryFilter.limit + 1,
              onChange: (page, limit) =>
                setClusterQueryFilter({
                  limit,
                  offset: (page - 1) * limit
                }),
              showSizeChanger: true,
              hideOnSinglePage: true
            }}
          />
        </>
      )
    },
    {
      key: 'custom-deprecated-apis',
      label: 'Custom Deprecated APIs',
      children: (
        <>
          <Row className='mb-2' justify='space-between'>
            <h2 className='font-bold mb-2 text-2xl'>Custom Deprecated APIs</h2>
          </Row>

          <div className='mb-4'>
            <Flex className='mb-2' justify='space-between'>
              <Flex className='grow'>
                <div className='w-1/4 pr-2 font-bold'>Deprecated API Version</div>
                <div className='w-1/4 pr-2 font-bold'>New API Version</div>
                <div className='w-1/4 pr-2 font-bold'>Deprecated in</div>
                <div className='w-1/4 pr-2 font-bold'>Unavailable in</div>
              </Flex>

              <div style={{ width: '64px' }} className='text-center'></div>
            </Flex>
            {formValues.customDeprecatedApis.length == 0 ? (
              <div>No deprecated APIs</div>
            ) : (
              formValues.customDeprecatedApis.map((api, index) => (
                <Flex className='mb-2' justify='space-between' key={index}>
                  <Flex className='grow'>
                    <div className='w-1/4 pr-2'>
                      <Input
                        className='w-full'
                        placeholder='group/version. e.g., apps/v1'
                        value={api.apiVersion}
                        onChange={(e) => {
                          const newAPIs = [...formValues.customDeprecatedApis];
                          newAPIs[index].apiVersion = e.target.value;
                          setValue('customDeprecatedApis', newAPIs);
                        }}
                      />
                    </div>
                    <div className='w-1/4 pr-2'>
                      <Input
                        className='w-full'
                        placeholder='group/version. e.g., apps/v1'
                        value={api.newApiVersion}
                        onChange={(e) => {
                          const newAPIs = [...formValues.customDeprecatedApis];
                          newAPIs[index].newApiVersion = e.target.value;
                          setValue('customDeprecatedApis', newAPIs);
                        }}
                      />
                    </div>
                    <div className='w-1/4 pr-2'>
                      <Input
                        className='w-full'
                        placeholder='Kubernetes Version. e.g., 1.26'
                        value={api.deprecatedInKubernetesVersion}
                        onChange={(e) => {
                          const newAPIs = [...formValues.customDeprecatedApis];
                          newAPIs[index].deprecatedInKubernetesVersion = e.target.value;
                          setValue('customDeprecatedApis', newAPIs);
                        }}
                      />
                    </div>
                    <div className='w-1/4 pr-2'>
                      <Input
                        className='w-full'
                        placeholder='Kubernetes Version. e.g., 1.26'
                        value={api.unavailableInKubernetesVersion}
                        onChange={(e) => {
                          const newAPIs = [...formValues.customDeprecatedApis];
                          newAPIs[index].unavailableInKubernetesVersion = e.target.value;
                          setValue('customDeprecatedApis', newAPIs);
                        }}
                      />
                    </div>
                  </Flex>

                  <div style={{ width: '64px' }} className='text-center'>
                    <Button
                      onClick={() =>
                        setValue(
                          'customDeprecatedApis',
                          formValues.customDeprecatedApis.filter((_, i) => i !== index)
                        )
                      }
                    >
                      <FontAwesomeIcon icon={faTrash} />
                    </Button>
                  </div>
                </Flex>
              ))
            )}
          </div>

          {showAlert && kubevisionDashboardUpdate.error && (
            <Alert
              className='mb-4'
              type='error'
              message={errorToString(kubevisionDashboardUpdate.error)}
              banner
            />
          )}

          <Flex>
            <Button
              className='mr-2'
              onClick={() =>
                setValue('customDeprecatedApis', [
                  ...formValues.customDeprecatedApis,
                  CustomDeprecatedAPI.fromJson({})
                ])
              }
            >
              <FontAwesomeIcon icon={faPlus} />
              Add Deprecated API
            </Button>
          </Flex>
        </>
      )
    },
    {
      key: 'cve-scan',
      label: 'CVE Scan',
      children: (
        <>
          <Row className='mb-2' justify='space-between'>
            <h2 className='font-bold text-2xl'>CVE Scan</h2>
          </Row>
          <Row className='mb-4' justify='start'>
            <div className='text-sm text-gray-500'>
              The CVE Scan feature requires the agent to be upgraded to version 0.5.49 or higher.
            </div>
          </Row>
          <Row className='mt-2 w-full' align='middle' style={{ maxWidth: '400px' }}>
            <Col span={12}>
              <label className='mr-2 text-lg'>Enabled</label>
            </Col>
            <Col span={12}>
              <Switch
                loading={kubevisionDashboardUpdate.isPending}
                checked={formValues.kubeVisionConfig?.cveScanConfig?.scanEnabled}
                onChange={(checked) => {
                  setValue('kubeVisionConfig', {
                    ...formValues.kubeVisionConfig,
                    cveScanConfig: {
                      ...formValues.kubeVisionConfig?.cveScanConfig,
                      scanEnabled: checked
                    }
                  });
                }}
              />
            </Col>
          </Row>
          {formValues.kubeVisionConfig?.cveScanConfig?.scanEnabled && (
            <Row className='mt-2 w-full' align='middle' style={{ maxWidth: '400px' }}>
              <Col span={12}>
                <label className='mr-2 text-lg'>Rescan Interval</label>
              </Col>
              <Col span={12}>
                <Select
                  className='w-full'
                  value={formValues.kubeVisionConfig?.cveScanConfig?.rescanInterval || '8h'}
                  options={[
                    { label: '8 hours', value: '8h' },
                    { label: '1 day', value: '24h' },
                    { label: '3 days', value: '72h' },
                    { label: '7 days', value: '168h' }
                  ]}
                  onChange={(value) => {
                    setValue('kubeVisionConfig', {
                      ...formValues.kubeVisionConfig,
                      cveScanConfig: {
                        ...formValues.kubeVisionConfig?.cveScanConfig,
                        rescanInterval: value
                      }
                    });
                  }}
                />
              </Col>
            </Row>
          )}
        </>
      )
    },
    {
      key: 'incidents',
      label: 'Incidents',
      hidden: currentOrgFeatureStatuses.aiSupportEngineer !== FeatureStatus.ENABLED,
      children: <IncidentsTab form={form} show={show} instance={instance} confirm={confirm} />
    }
  ];

  return (
    <>
      <SectionHeader
        saveSettings={{
          run: handleSubmit((data) => kubevisionDashboardUpdate.mutate(data)),
          loading: kubevisionDashboardUpdate.isPending,
          reset: () => {
            reset();
            setShowAlert(false);
          },
          done: kubevisionDashboardUpdate.isSuccess
        }}
      >
        Akuity Intelligence
      </SectionHeader>

      {instanceName && instanceName()}

      {showInstanceVersionWarning && (
        <Row className={'mt-2 mb-8'}>
          <Tag color={'red'} className={'py-4 px-2'}>
            <b>Warning!:</b>
            To use Akuity Intelligence, the proprietary version of ArgoCD provided by Akuity is
            required, with a version suffix ending in <b>-ak</b>. The version must be at least{' '}
            <b>ak.{MINIMUM_ARGO_VERSION}</b>.<br />
            <Flex>
              <b>Current version:</b> {instance?.version}
              <div
                className={'ml-2 underline cursor-pointer'}
                onClick={handleClickUpdateNow}
                onKeyDown={() => {}}
              >
                Upgrade now!
              </div>
            </Flex>
          </Tag>
        </Row>
      )}

      <Row className='mt-2 mb-4' align='middle'>
        <label className='mr-2 text-lg'>Enabled</label>
        <Controller
          control={control}
          name='multiClusterK8sDashboardEnabled'
          render={({ field }) => (
            <Switch checked={field.value} onChange={(checked) => field.onChange(checked)} />
          )}
        />
      </Row>

      {formValues.multiClusterK8sDashboardEnabled && (
        <>
          <Tabs items={tabs.filter((tab) => !tab.hidden)} />
        </>
      )}
    </>
  );
};
