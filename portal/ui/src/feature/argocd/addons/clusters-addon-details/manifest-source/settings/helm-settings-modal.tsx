import { zod<PERSON>esolver } from '@hookform/resolvers/zod';
import { Al<PERSON>, Divider, Empty, Flex, Input, Modal, Typography } from 'antd';
import { Controller, useFieldArray, useForm } from 'react-hook-form';
import { z } from 'zod';

import { AddonSpec } from '@ui/lib/apiclient/argocd/v1/argocd_pb';
import { FormContainer } from '@ui/lib/components/forms';
import { FieldContainer } from '@ui/lib/components/forms/field-container';
import { ModalProps } from '@ui/lib/hooks';

import { ManifestSourceItem } from '../types';

type Props = ModalProps & {
  data: ManifestSourceItem;
  onUpdate: (data: ManifestSourceItem) => void;
  disableEditing: boolean;
  addonSpec: AddonSpec;
};

export const HelmSettingsModal = ({
  visible,
  hide,
  data,
  onUpdate,
  disableEditing,
  addonSpec
}: Props) => {
  const { control, handleSubmit } = useForm<ManifestSourceFormType>({
    defaultValues: {
      values: [
        // Get current values
        ...Object.entries(data.manifestSource.helmSource?.values || {}).map(([key, value]) => ({
          key,
          value
        })),
        // Add default values
        ...(addonSpec.helmValues?.yamlPaths || [])
          .filter((i) => !data.manifestSource.helmSource?.values?.[i])
          .map((i) => ({ key: i, value: '' }))
      ],
      dependencies: data.manifestSource.helmSource
        ? [...data.manifestSource.helmSource.dependencies.map((i) => ({ ...i }))]
        : []
    },
    resolver: zodResolver(schema, {}, { raw: true })
  });

  const update = handleSubmit(({ values, dependencies }) => {
    onUpdate({
      manifestSource: {
        path: data.manifestSource.path,
        helmSource: {
          values: values.reduce((acc, curr) => ({ ...acc, [curr.key]: curr.value }), {}),
          dependencies
        }
      },
      name: data.name,
      type: data.type
    });
    hide();
  });

  const values = useFieldArray({ name: 'values', control });
  const dependencies = useFieldArray({ name: 'dependencies', control });

  return (
    <Modal
      title={`${data.name || data.type} - settings`}
      open={visible}
      onCancel={hide}
      okText='Update'
      onOk={update}
      width={720}
      okButtonProps={{ disabled: disableEditing }}
    >
      {disableEditing && (
        <Alert
          className='mb-4'
          banner
          message='Source updates are not allowed for disabled Addons'
        />
      )}
      {dependencies?.fields?.length > 0 && (
        <Typography.Title level={5} className='mt-6'>
          Chart Dependencies
        </Typography.Title>
      )}
      {dependencies?.fields?.map((item, index) => (
        <FormContainer key={item.id} noBorder label={`Dependency #${index + 1}`}>
          <Flex gap={16} flex={1}>
            <Controller
              control={control}
              name={`dependencies.${index}.name`}
              render={({ field }) => <Input {...field} placeholder='name' disabled />}
            />
            <Controller
              control={control}
              name={`dependencies.${index}.version`}
              render={({ field }) => (
                <Input {...field} placeholder='version' disabled={disableEditing} />
              )}
            />
          </Flex>
        </FormContainer>
      ))}
      <Divider />
      <Typography.Title level={5}>Helm Values</Typography.Title>
      {values.fields.length === 0 && (
        <Empty description='No Helm Values found. Add Helm Source Values in your Addon Config to fetch manifest source info.' />
      )}
      {values.fields?.map((item, index) => (
        <div key={item.id}>
          <FieldContainer control={control} name={`values.${index}.value`} label={item.key}>
            {({ field }) => <Input.TextArea {...field} />}
          </FieldContainer>
        </div>
      ))}
    </Modal>
  );
};

const schema = z.object({
  values: z.array(
    z.object({
      key: z.string(),
      value: z.string()
    })
  ),
  dependencies: z
    .array(
      z.object({
        name: z.string(),
        version: z.string()
      })
    )
    .optional()
});

type ManifestSourceFormType = z.infer<typeof schema>;
