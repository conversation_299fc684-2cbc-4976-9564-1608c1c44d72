import { useNavigate, useSearchParams } from 'react-router-dom';

type SearchParamsOptions = {
  replace?: boolean;
  overwrite?: boolean;
};

const useCustomSearchParams = () => {
  const navigate = useNavigate();
  const [search] = useSearchParams();

  const getSearchParam = (key: string): string | null => {
    return search.get(key);
  };

  const getSearchParamPagination = (prefix?: string) => {
    const result = { limit: 20, offset: 0, orderBy: null as string };
    const limitKey = prefix ? `${prefix}_limit` : 'limit';
    const offsetKey = prefix ? `${prefix}_offset` : 'offset';
    const orderByKey = prefix ? `${prefix}_orderBy` : 'orderBy';

    const limit = Number.parseInt(getSearchParam(limitKey));
    if (!Number.isNaN(limit) && limit > 0) {
      result.limit = limit < 20 ? 20 : limit;
    }
    const offset = Number.parseInt(getSearchParam(offsetKey));
    if (!Number.isNaN(offset) && offset > 0) {
      result.offset = offset;
    }
    result.orderBy = getSearchParam(orderByKey);
    return result;
  };

  const setSearchParams = (
    params: Record<string, string>,
    options: SearchParamsOptions = {
      replace: false,
      overwrite: false
    }
  ) => {
    const { overwrite, replace } = options;
    const searchParams = new URLSearchParams(overwrite ? '' : search.toString());
    Object.keys(params).forEach((key) => {
      if (params[key]) {
        searchParams.set(key, params[key]);
      } else {
        searchParams.delete(key);
      }
    });
    navigate({ search: searchParams.toString() }, { replace, preventScrollReset: true });
  };

  return {
    getSearchParam,
    getSearchParamPagination,
    setSearchParams
  };
};

export default useCustomSearchParams;
