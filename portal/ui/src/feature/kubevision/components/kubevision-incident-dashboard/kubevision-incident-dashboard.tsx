import { Card, TabsProps } from 'antd';
import { useEffect, useState } from 'react';

import useCustomSearchParams from '../../hooks/use-custom-search-params';

import { KubevisionIncidentIncidents } from './kubevision-incident-incidents';
import { KubevisionIncidentRunbook } from './kubevision-incident-runbook';

type IncidentDashboard = {
  instanceId: string;
};

export const KubevisionIncidentDashboard = ({ instanceId }: IncidentDashboard) => {
  const { getSearchParam, setSearchParams } = useCustomSearchParams();
  const [activeTab, setActiveTab] = useState('incidents');

  const items: TabsProps['items'] = [
    {
      key: 'incidents',
      label: 'Incidents'
    },
    {
      key: 'runbooks',
      label: 'Runbooks'
    }
  ];

  useEffect(() => {
    const urlTab = getSearchParam('incidentTab');
    if (!urlTab) {
      setSearchParams({ incidentTab: activeTab }, { replace: true });
    }

    if (items.map((i) => i.key).includes(urlTab) && urlTab !== activeTab) {
      setActiveTab(urlTab);
    }
  }, [getSearchParam('incidentTab'), activeTab]);

  return (
    <Card
      className='mb-2'
      tabList={items}
      activeTabKey={activeTab}
      onTabChange={(key) => {
        setActiveTab(key);
        setSearchParams({ incidentTab: key });
      }}
    >
      {activeTab === 'incidents' && <KubevisionIncidentIncidents instanceId={instanceId} />}
      {activeTab === 'runbooks' && <KubevisionIncidentRunbook instanceId={instanceId} />}
    </Card>
  );
};
