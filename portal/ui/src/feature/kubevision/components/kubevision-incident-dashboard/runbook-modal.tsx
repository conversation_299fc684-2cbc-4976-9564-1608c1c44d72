import { PlainMessage } from '@bufbuild/protobuf';
import { zodResolver } from '@hookform/resolvers/zod';
import { Input, Modal, Select, ConfigProvider, theme, Flex } from 'antd';
import { useMemo, useState } from 'react';
import { useForm } from 'react-hook-form';
import { z } from 'zod';

import { Instance, Runbook } from '@ui/lib/apiclient/argocd/v1/argocd_pb';
import { ClusterResource } from '@ui/lib/apiclient/organization/v1/organization_pb';
import { CodeEditor } from '@ui/lib/components/code-editor';
import { FieldContainer } from '@ui/lib/components/forms/field-container';
import { useKubeVisionContext } from '@ui/lib/context/kubevision-context';
import { zodValidators } from '@ui/lib/utils';

export const RunbookModal = ({
  index = -1,
  visible,
  hide,
  runbooks,
  setRunbooks,
  instanceId,
  instances,
  showInstanceSelect,
  isDarkTheme,
  resources
}: {
  visible: boolean;
  hide: () => void;
  runbooks?: PlainMessage<Runbook>[];
  setRunbooks?: (runbooks: PlainMessage<Runbook>[], instanceId?: string) => void;
  index?: number;
  instanceId?: string;
  instances?: Instance[];
  showInstanceSelect?: boolean;
  isDarkTheme?: boolean;
  resources?: ClusterResource[];
}) => {
  const [appliedToClusters, setAppliedToClusters] = useState<string[]>([]);
  const runbookItem = {
    name: runbooks?.[index]?.name || '',
    content: runbooks?.[index]?.content || '',
    instanceId: instanceId || '',
    appliedTo: runbooks?.[index]?.appliedTo || {
      argocdApplications: [],
      k8sNamespaces: [],
      clusters: []
    }
  };
  const form = useForm({
    defaultValues: runbookItem,
    resolver: zodResolver(
      z.object({
        instanceId: showInstanceSelect ? zodValidators.requiredString : z.string().optional(),
        name: zodValidators.resourceName.refine(
          (val) => !runbooks.find((item, i) => i !== index && item.name === val),
          {
            message: 'Runbook with this name already exists'
          }
        ),
        content: zodValidators.requiredString,
        appliedTo: z.object({
          argocdApplications: z.array(z.string()),
          k8sNamespaces: z.array(z.string()),
          clusters: z.array(z.string())
        })
      })
    )
  });
  const submit = form.handleSubmit(async (runbookItem) => {
    const runbook: PlainMessage<Runbook> = {
      name: runbookItem.name,
      content: runbookItem.content,
      appliedTo: runbookItem.appliedTo
    };
    setRunbooks?.(
      index === -1
        ? runbooks.concat(runbook)
        : [...runbooks.slice(0, index), runbook, ...runbooks.slice(index + 1)],
      runbookItem.instanceId
    );
    form.reset();
    hide();
  });

  const { enabledClustersInfo } = useKubeVisionContext();
  const applications = useMemo(() => {
    const applications: string[] = [];
    resources.forEach((resource) => {
      if (resource.kind === 'Application') {
        applications.push(resource.name);
      }
    });
    return applications;
  }, [resources]);
  const clusters = useMemo(() => {
    const clusters: string[] = [];
    resources.forEach((resource) => {
      if (resource.kind === 'Namespace') {
        clusters.push(enabledClustersInfo.clusterName(resource.clusterId));
      }
    });
    return [...new Set(clusters)];
  }, [resources, instanceId, enabledClustersInfo]);
  const namespaces = useMemo(() => {
    const namespaces: string[] = [];
    resources.forEach((r) => {
      if (r.kind !== 'Namespace') {
        return;
      }
      const selectedClusterNames = [];
      if (appliedToClusters.length === 0) {
        selectedClusterNames.push(...clusters);
      } else if (appliedToClusters.includes('*')) {
        selectedClusterNames.push(...clusters);
      } else {
        selectedClusterNames.push(...appliedToClusters);
      }
      if (!selectedClusterNames.includes(enabledClustersInfo.clusterName(r.clusterId))) {
        return;
      }
      namespaces.push(r.name);
    });
    return [...new Set(namespaces)];
  }, [resources, clusters, appliedToClusters, enabledClustersInfo]);

  const { token } = theme.useToken();

  return (
    <ConfigProvider
      theme={{
        algorithm: isDarkTheme ? theme.darkAlgorithm : theme.defaultAlgorithm
      }}
    >
      <Modal
        title={index > -1 ? 'Update Runbook' : 'New Runbook'}
        open={visible}
        onCancel={hide}
        onOk={() => submit()}
        width={1200}
      >
        {showInstanceSelect && (
          <FieldContainer
            label='Instance'
            control={form.control}
            description='Runbook instance'
            name='instanceId'
          >
            {({ field }) => (
              <Select
                className='w-[200px]'
                options={instances?.map((i) => ({
                  label: i.name,
                  value: i.id
                }))}
                value={field.value}
                onChange={field.onChange}
              />
            )}
          </FieldContainer>
        )}
        <FieldContainer label='Name' control={form.control} description='Runbook name' name='name'>
          {({ field }) => <Input value={field.value} onChange={field.onChange} />}
        </FieldContainer>

        <FieldContainer
          label='Applied To'
          control={form.control}
          description='Runbook applied to'
          name='appliedTo'
        >
          {({ field }) => {
            return (
              <Flex gap='middle' className='mb-4'>
                <Flex
                  gap='small'
                  className='p-2 rounded-md'
                  style={{
                    backgroundColor: token.colorBgLayout
                  }}
                >
                  <div>
                    Argo CD Apps:{' '}
                    <Select
                      value={field.value.argocdApplications || []}
                      options={[
                        { label: '*', value: '*' },
                        ...applications.map((app) => ({
                          label: app,
                          value: app
                        }))
                      ]}
                      onChange={(v) => field.onChange({ ...field.value, argocdApplications: v })}
                      className='inline-block min-w-[160px]'
                      mode='tags'
                    />
                  </div>
                </Flex>
                <Flex
                  gap='small'
                  className='p-2 rounded-md'
                  style={{
                    backgroundColor: token.colorBgLayout
                  }}
                >
                  <div>
                    K8S Namespaces:{' '}
                    <Select
                      value={field.value.k8sNamespaces || []}
                      options={[
                        { label: '*', value: '*' },
                        ...namespaces.map((ns) => ({
                          label: ns,
                          value: ns
                        }))
                      ]}
                      onChange={(v) => field.onChange({ ...field.value, k8sNamespaces: v })}
                      className='inline-block min-w-[160px]'
                      mode='tags'
                    />
                  </div>
                  <div>
                    Clusters:{' '}
                    <Select
                      value={field.value.clusters || []}
                      options={[
                        { label: '*', value: '*' },
                        ...clusters.map((cluster) => ({
                          label: cluster,
                          value: cluster
                        }))
                      ]}
                      onChange={(v) => {
                        setAppliedToClusters(v);
                        field.onChange({ ...field.value, clusters: v });
                      }}
                      className='inline-block min-w-[160px]'
                      mode='tags'
                    />
                  </div>
                </Flex>
              </Flex>
            );
          }}
        </FieldContainer>

        <FieldContainer
          label='Content'
          control={form.control}
          description='Runbook content'
          name='content'
        >
          {({ field }) => (
            <CodeEditor
              theme={isDarkTheme ? 'github_dark' : 'github_light_default'}
              mode={{
                name: 'markdown',
                options: {}
              }}
              value={field.value}
              onChange={field.onChange}
            />
          )}
        </FieldContainer>
      </Modal>
    </ConfigProvider>
  );
};
