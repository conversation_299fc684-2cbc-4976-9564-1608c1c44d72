import { faSitemap } from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { Button, Flex } from 'antd';
import { MutableRefObject } from 'react';

import { ExplorerFilter } from '@ui/feature/kubevision/components/kubevision-explorer-dashboard/kubevision-explorer-filter';
import useCustomSearchParams from '@ui/feature/kubevision/hooks/use-custom-search-params';
import { useKubeVisionContext } from '@ui/lib/context/kubevision-context';

import { KubeVisionExplorerTableRef } from './kubevision-explorer-table';

type Props = {
  filter: ExplorerFilter;
  explorerTableRef: MutableRefObject<KubeVisionExplorerTableRef | null>;
  onFilterChange: (filter: Partial<ExplorerFilter>, overwrite?: boolean, replace?: boolean) => void;
};

export const TreeViewBtn = ({ filter, explorerTableRef, onFilterChange }: Props) => {
  const { enabledClustersInfo } = useKubeVisionContext();
  const { getSearchParam } = useCustomSearchParams();
  const isTreeView = getSearchParam('treeView') === 'true';

  const handleViewChange = () => {
    const firstResource = explorerTableRef.current?.getFirstResource?.();
    const firstClusterId = firstResource?.clusterId;
    const firstInstanceId = firstResource?.instanceId;

    let cluster = firstClusterId ? enabledClustersInfo.clusterName(firstClusterId) : undefined;
    let instance = firstInstanceId ? enabledClustersInfo.instanceName(firstInstanceId) : undefined;

    // When entering tree view, ensure we have sensible defaults for cluster/instance
    if (!isTreeView && !cluster) {
      if (filter.instance) {
        const instId = enabledClustersInfo.instanceId(filter.instance);
        const names = enabledClustersInfo.clusterNames({ instanceId: instId });
        cluster = names?.[0];
        instance = filter.instance;
      } else {
        const all = enabledClustersInfo.clusters();
        const first = all?.[0];
        if (first) {
          cluster = enabledClustersInfo.clusterName(first.clusterId);
          instance = enabledClustersInfo.instanceName(first.instanceId);
        }
      }
    }

    onFilterChange(
      {
        treeView: !isTreeView,
        cluster: isTreeView ? getSearchParam('cluster') : cluster,
        instance: isTreeView ? getSearchParam('instance') : instance,
        namespace: !isTreeView ? (getSearchParam('namespace') ? filter.namespace : 'default') : ''
      },
      false,
      false
    );
  };

  const normalStyle = {
    background: `linear-gradient(35deg, hsl(0, 35%, 55%), hsl(230, 55%, 55%))`,
    border: 'none',
    color: 'white'
  };

  const activeStyle = {
    background: `linear-gradient(35deg, hsl(0, 35%, 70%), hsl(230, 55%, 70%))`,
    border: 'none',
    color: 'white'
  };

  return (
    <Flex vertical={true} justify='center' align='middle'>
      <Button
        // type={isTreeView ? 'primary' : 'default'}
        onClick={handleViewChange}
        style={isTreeView ? activeStyle : normalStyle}
      >
        <FontAwesomeIcon icon={faSitemap} />
        Tree View
      </Button>
    </Flex>
  );
};
