import {
  faMagnifyingGlass,
  faHeart,
  faPauseCircle,
  faHeartBroken,
  faCircleNotch,
  faGhost,
  faQuestionCircle,
  faExternalLink
} from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { Select, Flex, Input, Button } from 'antd';
import { useState } from 'react';
import { useSearchParams } from 'react-router-dom';

import {
  defaultExplorerFilter,
  ExplorerFilter,
  filterToListKubernetesResourcesRequest
} from '@ui/feature/kubevision/components/kubevision-explorer-dashboard/kubevision-explorer-filter';
import { ClusterSelect } from '@ui/feature/kubevision/components/shared/cluster-select';
import { FilterBar } from '@ui/feature/kubevision/components/shared/filter-bar';
import { KindSelect } from '@ui/feature/kubevision/components/shared/kind-select';
import { NamespaceSelect } from '@ui/feature/kubevision/components/shared/namespace-select';
import { OwnerTag } from '@ui/feature/kubevision/components/shared/owner-tag';
import { DashboardType } from '@ui/feature/kubevision/const';
import { usePlatformContext } from '@ui/feature/shared/context/platform-context';
import { useGetKubernetesResourceListQuery } from '@ui/lib/apiclient/organization/kubevision-queries';
import { HealthStatus } from '@ui/lib/apiclient/organization/v1/organization_pb';
import { GroupVersionKind } from '@ui/lib/apiclient/types/k8s/v1/k8s_pb';
import { getQueryParamsAsString } from '@ui/lib/apiclient/utils';
import { useKubeVisionContext } from '@ui/lib/context/kubevision-context';

import { IncidentLink } from '../shared/incident-link';
import { InstanceSelect } from '../shared/instance-select';

type Props = {
  instanceId: string;
  filter: ExplorerFilter;
  onFilterChange: (filter: Partial<ExplorerFilter>, overwrite?: boolean) => void;
};

const HealthStatusIcon = ({ state }: { state: HealthStatus }) => {
  let color = '#CCD6DD';
  let icon = faQuestionCircle;

  switch (state) {
    case HealthStatus.HEALTHY:
      color = '#18BE94';
      icon = faHeart;
      break;
    case HealthStatus.SUSPENDED:
      color = '#766f94';
      icon = faPauseCircle;
      break;
    case HealthStatus.DEGRADED:
      color = '#E96D76';
      icon = faHeartBroken;
      break;
    case HealthStatus.PROGRESSING:
      color = '#0DADEA';
      icon = faCircleNotch;
      break;
    case HealthStatus.MISSING:
      color = '#f4c030';
      icon = faGhost;
      break;
  }

  return (
    <FontAwesomeIcon
      qe-id='utils-health-status-title'
      title={state.toString()}
      icon={icon}
      style={{ color }}
    />
  );
};

const healthStatusOptions = [
  { label: 'Healthy', value: HealthStatus.HEALTHY },
  { label: 'Degraded', value: HealthStatus.DEGRADED },
  { label: 'Missing', value: HealthStatus.MISSING },
  { label: 'Unknown', value: HealthStatus.UNKNOWN },
  { label: 'Progressing', value: HealthStatus.PROGRESSING },
  { label: 'Suspended', value: HealthStatus.SUSPENDED }
];

const HealthStatusSelect = ({
  healthStatus,
  onChange
}: {
  healthStatus: HealthStatus[];
  onChange: (value: HealthStatus[]) => void;
}) => {
  const healthStatusValues = (healthStatus || []).map((status) => Number(status));

  return (
    <Flex className='items-center my-1 mr-4' justify='start' align='middle'>
      <strong className='mr-2'>Health Status:</strong>
      <Select
        className='w-auto !rounded-md'
        popupMatchSelectWidth={false}
        placeholder='All'
        mode='multiple'
        value={healthStatusValues}
        style={{ minWidth: 180 }}
        onChange={onChange}
        options={healthStatusOptions}
        optionRender={(option) => (
          <Flex align='center' justify='start'>
            <HealthStatusIcon state={option.value as HealthStatus} />
            <span className='ml-1'>{option.label}</span>
          </Flex>
        )}
        allowClear={true}
      />
    </Flex>
  );
};

const SearchName = ({
  search,
  onChange
}: {
  search: string;
  onChange: (value: string) => void;
}) => {
  const [searchText, setSearchText] = useState<string>(search);

  const handleKeyDown = (event: React.KeyboardEvent<HTMLInputElement>) => {
    if (event.key === 'Enter') {
      onChange(searchText);
    }
  };

  return (
    <Flex className='items-center mr-4' justify='start'>
      <strong className='mr-2'>Search:</strong>
      <Input
        className='mr-4 w-[390px]'
        addonBefore={<FontAwesomeIcon icon={faMagnifyingGlass} />}
        placeholder='Search Name'
        value={searchText}
        onChange={(e) => {
          const newValue = e.target.value;
          setSearchText(newValue);
          if (newValue === '') {
            onChange('');
          }
        }}
        onKeyDown={handleKeyDown}
        allowClear={true}
      />
    </Flex>
  );
};

export const KubeVisionExplorerFilterBar = ({ instanceId, filter, onFilterChange }: Props) => {
  const { apiPathPrefix, openUrlWithCookies } = usePlatformContext();
  const { organizationMode, organizationId, enabledClustersInfo } = useKubeVisionContext();
  const [, setSearchParams] = useSearchParams();

  const { data: namespaceDetails, isLoading: isLoadingNamespaceDetails } =
    useGetKubernetesResourceListQuery(
      filterToListKubernetesResourcesRequest(
        enabledClustersInfo,
        {
          cluster: filter.cluster,
          kind: 'Namespace',
          group: '',
          version: 'v1',
          name: '',
          nameContains: filter.namespace
        },
        instanceId
      ),
      {
        enabled: filter.treeView && !!filter.namespace
      }
    );

  const onExportToCSV = () => {
    const params = filterToListKubernetesResourcesRequest(enabledClustersInfo, filter, instanceId);
    const url = `${apiPathPrefix}stream/orgs/${organizationId}/k8s/resources-csv?${getQueryParamsAsString(params)}`;
    openUrlWithCookies(url, '_blank');
  };

  const clusterId = enabledClustersInfo.clusterId({
    instanceId,
    clusterName: filter.cluster
  });

  return (
    <FilterBar
      firstRow={
        <>
          {filter.nodeId && <strong className='pr-4 mr-4 border-r '>Pods </strong>}

          {organizationMode && !filter.nodeId && (
            <InstanceSelect
              instanceId={enabledClustersInfo.instanceId(filter.instance)}
              showAllOption={!filter.treeView}
              onChange={(instanceId) =>
                onFilterChange({
                  instance: enabledClustersInfo.instanceName(instanceId),
                  cluster: filter.treeView
                    ? enabledClustersInfo.clusterNames({ instanceId })?.[0]
                    : undefined,
                  ownerId: undefined,
                  offset: 0
                })
              }
            />
          )}

          {!filter.nodeId && (
            <ClusterSelect
              instanceId={instanceId}
              clusterId={clusterId}
              showAllOption={!filter.treeView}
              onChange={(clusterId, instanceId) =>
                onFilterChange({
                  cluster: enabledClustersInfo.clusterName(clusterId),
                  instance: enabledClustersInfo.instanceName(instanceId),
                  ownerId: undefined,
                  offset: 0
                })
              }
            />
          )}

          <NamespaceSelect
            instanceId={instanceId}
            clusterId={enabledClustersInfo.clusterId({
              instanceId,
              clusterName: filter.cluster || filter.nodeClusterName
            })}
            namespace={filter.namespace}
            treeView={filter.treeView}
            groupVersionKind={GroupVersionKind.fromJson({
              group: filter.group || '',
              version: filter.version || '',
              kind: filter.kind || ''
            })}
            onChange={(namespace) =>
              onFilterChange({
                namespace: namespace,
                ownerId: undefined,
                offset: 0
              })
            }
          />

          {!filter.nodeId && !filter.treeView && (
            <KindSelect
              instanceId={instanceId}
              kind={filter.kind}
              version={filter.version}
              group={filter.group}
              onChange={(gvk, clusterScoped) =>
                onFilterChange({
                  group: gvk.group,
                  version: gvk.version,
                  kind: gvk.kind,
                  ownerId: undefined,
                  offset: 0,
                  namespace: !clusterScoped ? filter.namespace : undefined
                })
              }
            />
          )}

          {!filter.nodeId && filter.treeView && (
            <KindSelect
              instanceId={instanceId}
              mode='multiple'
              kind={filter.treeViewResourceKinds || []}
              onMultipleChange={(gvk) =>
                onFilterChange({
                  treeViewResourceKinds: gvk
                })
              }
            />
          )}

          {filter.treeView && (
            <HealthStatusSelect
              healthStatus={filter.treeViewHealthStatuses || []}
              onChange={(value) => onFilterChange({ treeViewHealthStatuses: value })}
            />
          )}
        </>
      }
      secondRow={
        (filter.treeView || (!filter.treeView && filter.ownerId)) && (
          <Flex align='space-between' className='w-full'>
            <Flex flex={1}>
              {!filter.treeView && filter.ownerId && (
                <OwnerTag
                  instanceId={instanceId}
                  ownerId={filter.ownerId}
                  clusterId={clusterId}
                  onClear={() => onFilterChange({ ownerId: undefined, offset: 0 })}
                />
              )}

              {filter.treeView && (
                <SearchName
                  search={filter.treeViewNameContains}
                  onChange={(v) => onFilterChange({ treeViewNameContains: v })}
                />
              )}
            </Flex>

            {filter.treeView && filter.namespace && !isLoadingNamespaceDetails && (
              <Button
                className='rounded-full mr-2'
                onClick={() => {
                  const firstResource = namespaceDetails?.resources?.[0];
                  const resourceId = firstResource?.uid || '';

                  setSearchParams((prev) => {
                    const sp = new URLSearchParams(prev);
                    sp.set('tab', 'kubevision');
                    sp.set('dashboard', DashboardType.Explorer);
                    sp.set('version', 'v1');
                    sp.set('kind', 'Namespace');
                    sp.set('limit', '20');
                    sp.set('offset', '0');
                    sp.set('treeView', 'true');
                    sp.set('namespace', filter.namespace);
                    sp.set('treeViewResourceKinds', (filter.treeViewResourceKinds ?? []).join(','));
                    sp.set(
                      'treeViewHealthStatuses',
                      (filter.treeViewHealthStatuses ?? []).join(',')
                    );
                    sp.set('resourceId', resourceId);
                    sp.set('resourceInstance', enabledClustersInfo.instanceName(instanceId));
                    sp.set('resourceCluster', enabledClustersInfo.clusterName(clusterId));
                    sp.set('detailsTab', 'detailsTab_eventTimeline');
                    return sp;
                  });
                }}
              >
                Namespace
                <FontAwesomeIcon icon={faExternalLink} className='ml-1' />
              </Button>
            )}

            {filter.treeView && filter.namespace && (
              <IncidentLink
                instanceId={instanceId}
                namespace={filter.namespace}
                clusterId={enabledClustersInfo.clusterId({
                  instanceId,
                  clusterName: filter.cluster || filter.nodeClusterName
                })}
                clusterName={filter.cluster || filter.nodeClusterName}
              />
            )}
          </Flex>
        )
      }
      treeView={filter.treeView}
      onExportToCSV={onExportToCSV}
      onReset={() => onFilterChange(defaultExplorerFilter(), true)}
      hideResetButton={!!filter.nodeId || filter.treeView}
    />
  );
};
