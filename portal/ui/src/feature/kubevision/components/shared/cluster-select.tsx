import { Flex, Select, Tag } from 'antd';
import { useMemo } from 'react';

import { useGetKubernetesEnabledClustersQuery } from '@ui/lib/apiclient/organization/kubevision-queries';
import { useKubeVisionContext } from '@ui/lib/context/kubevision-context';

type ClusterSelectProps = {
  instanceId: string;
  clusterId?: string;
  hasDeprecatedApis?: boolean;
  onChange?: (clusterName?: string, instanceName?: string) => void;
  showAllOption?: boolean;
};

export const ClusterSelect = ({
  instanceId,
  clusterId,
  hasDeprecatedApis,
  onChange,
  showAllOption
}: ClusterSelectProps) => {
  const { organizationMode, enabledClustersInfo } = useKubeVisionContext();
  const shouldGroupByInstance = useMemo(() => organizationMode && !instanceId, [instanceId]);
  const { data, isLoading } = useGetKubernetesEnabledClustersQuery(
    { instanceId, hasDeprecatedApis },
    {
      enabled: hasDeprecatedApis,
      staleTime: 5000
    }
  );

  const value = clusterId ?? '';
  const options = useMemo(() => {
    const allOption = !shouldGroupByInstance
      ? {
          value: '',
          label: <>{'*'}</>,
          instanceId: undefined as string | undefined,
          clusterId: undefined as string | undefined
        }
      : {
          label: 'All Clusters',
          title: 'All Clusters',
          options: [
            {
              value: '',
              label: <>{'*'}</>,
              instanceId: undefined as string | undefined,
              clusterId: undefined as string | undefined
            }
          ]
        };

    const clusterOptions = showAllOption || !data || data.length === 0 ? [allOption] : [];

    const clusters = (data ?? enabledClustersInfo.clusters({ instanceId })).sort((a, b) =>
      a.clusterName.localeCompare(b.clusterName)
    );
    for (const cluster of clusters) {
      const option = {
        value: cluster.clusterId,
        label: (
          <>
            {cluster.clusterName}
            {cluster.isDegraded && (
              <sup className='ml-[8px]'>
                <Tag color='red' bordered={false} className='rounded-xl text-[8px] !leading-[14px]'>
                  failed to sync
                </Tag>
              </sup>
            )}
          </>
        ),
        instanceId: cluster.instanceId,
        clusterId: cluster.clusterId
      };
      if (shouldGroupByInstance) {
        let group = clusterOptions.find((c) => c.label === cluster.instanceName);
        if (!group) {
          group = {
            label: cluster.instanceName,
            title: cluster.instanceName,
            options: []
          };
          clusterOptions.push(group);
        }
        group.options.push(option);
      } else {
        clusterOptions.push(option);
      }
    }
    return clusterOptions;
  }, [showAllOption, data]);

  // Note: default selection for tree view is handled by TreeViewBtn to avoid URL history pollution

  return (
    <Flex className='items-center my-1 mr-4' justify='start' align='middle'>
      <strong className='mr-2'>Cluster:</strong>
      <Select
        className='w-auto !rounded-md'
        popupMatchSelectWidth={false}
        placeholder='Cluster'
        loading={isLoading}
        value={value}
        style={{ minWidth: 180 }}
        onChange={(_, option) => {
          if (!Array.isArray(option)) {
            onChange?.(option?.value, option?.instanceId);
          }
        }}
        options={options}
        showSearch={true}
        autoClearSearchValue={true}
        filterOption={(input, option) => {
          if (!option) return false;
          if ('options' in option) {
            return option.options?.some((opt) =>
              enabledClustersInfo
                .clusterName(opt.clusterId)
                ?.toString()
                .toLowerCase()
                .includes(input.toLowerCase())
            );
          }
          return enabledClustersInfo
            .clusterName(option.clusterId)
            ?.toString()
            .toLowerCase()
            .includes(input.toLowerCase());
        }}
      />
    </Flex>
  );
};
