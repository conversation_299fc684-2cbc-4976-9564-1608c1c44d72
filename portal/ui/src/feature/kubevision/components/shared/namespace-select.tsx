import { Flex, Select } from 'antd';
import { useMemo } from 'react';

import {
  useGetKubernetesNamespacesQuery,
  useGetKubernetesResourceTypesQuery
} from '@ui/lib/apiclient/organization/kubevision-queries';
import { GroupVersionKind } from '@ui/lib/apiclient/types/k8s/v1/k8s_pb';

type Props = {
  instanceId: string;
  clusterId?: string;
  namespace?: string;
  nodeName?: string;
  onChange?: (namespace?: string) => void;
  treeView?: boolean;
  groupVersionKind?: GroupVersionKind;
};

export const NamespaceSelect = ({
  instanceId,
  clusterId,
  namespace,
  nodeName,
  onChange,
  treeView,
  groupVersionKind
}: Props) => {
  const { data: resourceTypes, isLoading: isLoadingResourceTypes } =
    useGetKubernetesResourceTypesQuery(instanceId);
  const isClusterScoped = resourceTypes?.resourceTypes?.find(
    (item) =>
      item.groupVersionKind.group === groupVersionKind?.group &&
      item.groupVersionKind.version === groupVersionKind?.version &&
      item.groupVersionKind.kind === groupVersionKind?.kind
  )?.clusterScoped;

  const { data, isLoading: isLoadingNamespaces } = useGetKubernetesNamespacesQuery(
    {
      instanceId: instanceId,
      clusterIds: clusterId ? [clusterId] : undefined,
      nodeName
    },
    { enabled: treeView || (!treeView && !isClusterScoped) }
  );

  const isLoading = isLoadingResourceTypes || isLoadingNamespaces;

  const value = namespace ?? '';
  const options = useMemo(() => {
    const namespaceOptions = !treeView ? [{ value: '', label: '*' }] : [];

    if (data) {
      for (const namespace of data) {
        namespaceOptions.push({ value: namespace, label: namespace });
      }
    }
    return namespaceOptions.sort((a, b) => a.value.localeCompare(b.value));
  }, [!treeView, data]);

  return (
    <>
      {(treeView || (!treeView && !isClusterScoped)) && (
        <Flex className='items-center my-1 mr-4' justify='start' align='middle'>
          <strong className='mr-2'>Namespace:</strong>
          <Select
            className='w-auto !rounded-md'
            popupMatchSelectWidth={false}
            placeholder='Namespace'
            loading={isLoading}
            value={value}
            style={{ minWidth: 180 }}
            onChange={(value) => onChange?.(value)}
            options={options}
            showSearch={true}
            autoClearSearchValue={true}
            filterOption={(input, option) => {
              if (!option) return false;
              return option.label?.toString().toLowerCase().includes(input.toLowerCase());
            }}
          />
        </Flex>
      )}
    </>
  );
};
