import r2wc from '@r2wc/react-to-web-component';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { BrowserHistory } from 'history';
import { createContext, useContext, useEffect, useState } from 'react';
import { unstable_HistoryRouter as Router } from 'react-router-dom';

import { AISupportEngineer } from '@ui/feature/ai-support-engineer/ai-support-engineer';
import { PlatformContextProvider } from '@ui/feature/shared/context/platform-context-provider';
import { KargoSettings } from '@ui/lib/apiclient/extension/v1/extension_pb';
import { NotificationContextProvider } from '@ui/lib/context/notification-context';
import { useHistory } from '@ui/lib/hooks/use-history';
import type { ExtendedAIMessageContext } from '@ui/lib/types';

import { isAISupportEngineerEnabled } from './permission';

const ProjectPathname = 'project';
const IntelligencePathname = 'ext/intelligence';

interface RouteContextType {
  pathname: string;
  projectName: string;
  search: string;
}

const RouteContext = createContext<RouteContextType>({
  pathname: '',
  projectName: '',
  search: ''
});

const RouteProvider = ({ children }: { children: React.ReactNode }) => {
  const [routeState, setRouteState] = useState<RouteContextType>({
    pathname: window.location.pathname,
    projectName: '',
    search: window.location.search
  });

  const parseRouteParams = (pathname: string) => {
    const segments = pathname.split('/').filter(Boolean);
    const projectIndex = segments.indexOf(ProjectPathname);
    const projectName = projectIndex >= 0 ? segments[projectIndex + 1] || '' : '';
    return { pathname, projectName };
  };

  useHistory({
    onLocationChange: () => {
      const parsed = parseRouteParams(window.location.pathname);
      setRouteState({
        ...parsed,
        search: window.location.search
      });
    }
  });

  return <RouteContext.Provider value={routeState}>{children}</RouteContext.Provider>;
};

const KargoExtensionWrapper = ({
  instanceId,
  modelVersion,
  organizationId
}: {
  instanceId: string;
  modelVersion: string;
  organizationId: string;
}) => {
  const [computedDefaultContexts, setComputedDefaultContexts] =
    useState<ExtendedAIMessageContext[]>();

  const { pathname, projectName, search } = useContext(RouteContext);

  useEffect(() => {
    if (!instanceId) {
      return;
    }

    if (projectName) {
      setComputedDefaultContexts([
        {
          kargoProject: { name: projectName, instanceId }
        }
      ]);
    } else if (pathname.includes(IntelligencePathname) && search) {
      const params = new URLSearchParams(search);
      const namespace = params.get('namespace');
      const cluster = params.get('cluster');
      const instance = params.get('instance');

      if (namespace && cluster && instance) {
        setComputedDefaultContexts([
          {
            k8sNamespace: {
              name: namespace,
              instanceId: '',
              clusterId: '',
              clusterName: cluster,
              instanceName: instance
            }
          }
        ]);
      }
    } else {
      setComputedDefaultContexts([]);
    }
  }, [instanceId, pathname, projectName, search]);

  return (
    <AISupportEngineer
      isKubeVisionExtensionEnabled={true}
      organizationId={organizationId}
      instanceId={instanceId}
      modelVersion={modelVersion}
      defaultContexts={computedDefaultContexts}
    />
  );
};

export const mountAISupportEngineer = (settings: KargoSettings, browserHistory: BrowserHistory) => {
  if (!isAISupportEngineerEnabled(settings)) {
    return;
  }

  const AISREWrapper = () => {
    const queryClient = new QueryClient();
    return (
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      <Router history={browserHistory as any}>
        <NotificationContextProvider>
          <QueryClientProvider client={queryClient}>
            <PlatformContextProvider platform='kargo'>
              <RouteProvider>
                <KargoExtensionWrapper
                  organizationId={settings.organizationId}
                  instanceId={settings.instanceId}
                  modelVersion={settings.akuityIntelligence?.modelVersion}
                />
              </RouteProvider>
            </PlatformContextProvider>
          </QueryClientProvider>
        </NotificationContextProvider>
      </Router>
    );
  };

  customElements.define('ai-sre', r2wc(AISREWrapper));
  window.dispatchEvent(
    new CustomEvent('registerkargoextension', {
      detail: {
        type: 'layoutExtension',
        tagName: 'ai-sre'
      }
    })
  );
};
