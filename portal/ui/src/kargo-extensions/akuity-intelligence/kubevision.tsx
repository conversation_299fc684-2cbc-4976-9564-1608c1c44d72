import { faEye } from '@fortawesome/free-solid-svg-icons';
import r2wc from '@r2wc/react-to-web-component';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import {
  Chart as ChartJS,
  ArcElement,
  Tooltip,
  Legend,
  CategoryScale,
  LinearScale,
  TimeScale,
  BarElement,
  PointElement,
  LineElement
} from 'chart.js';
import { TreemapController, TreemapElement } from 'chartjs-chart-treemap';
import zoomPlugin from 'chartjs-plugin-zoom';
import { BrowserHistory } from 'history';
import { unstable_HistoryRouter as Router, useLocation } from 'react-router-dom';

import { KubeVision } from '@ui/feature/kubevision/kubevision';
import { PlatformContextProvider } from '@ui/feature/shared/context/platform-context-provider';
import { KargoSettings } from '@ui/lib/apiclient/extension/v1/extension_pb';
import { KubeVisionContextProvider } from '@ui/lib/context/kubevision-context';
import { NotificationContextProvider } from '@ui/lib/context/notification-context';

import { isAISupportEngineerEnabled, isKubeVisionEnabled } from './permission';

ChartJS.defaults.font.family = 'sans-serif';

ChartJS.register(
  ArcElement,
  Tooltip,
  Legend,
  CategoryScale,
  LinearScale,
  TimeScale,
  BarElement,
  Legend,
  PointElement,
  LineElement,
  zoomPlugin,
  TreemapController,
  TreemapElement
);

export const KubeVisionExtension = ({ settings }: { settings: KargoSettings }) => {
  // Force re-render when URL params change
  useLocation();

  return (
    <KubeVisionContextProvider
      organizationMode={true}
      organizationId={settings.organizationId}
      instanceId={''}
    >
      <div className={'p-4'} id='kubevision-extension'>
        <KubeVision isAISREFeatureOn={isAISupportEngineerEnabled(settings)} isk8sFeatureOn={true} />
      </div>
    </KubeVisionContextProvider>
  );
};

export const mountKubeVision = (settings: KargoSettings, history: BrowserHistory) => {
  if (!isKubeVisionEnabled(settings)) {
    return;
  }

  const KubeVisionExtensionWrapper = () => {
    const queryClient = new QueryClient();
    return (
      <NotificationContextProvider>
        <QueryClientProvider client={queryClient}>
          <PlatformContextProvider platform='kargo'>
            {/* eslint-disable-next-line @typescript-eslint/no-explicit-any */}
            <Router history={history as any}>
              <KubeVisionExtension settings={settings} />
            </Router>
          </PlatformContextProvider>
        </QueryClientProvider>
      </NotificationContextProvider>
    );
  };

  customElements.define('kubevision-extension', r2wc(KubeVisionExtensionWrapper));
  window.dispatchEvent(
    new CustomEvent('registerkargoextension', {
      detail: {
        type: 'appSubpage',
        label: 'Intelligence',
        path: 'intelligence',
        icon: faEye,
        tagName: 'kubevision-extension'
      }
    })
  );
};
