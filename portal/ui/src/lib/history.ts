import { create<PERSON>rowserHistory } from 'history';

// Single shared browser history for app and extensions
export const browserHistory = createBrowserHistory();

// Detect if running inside Argo CD extensions host
const isExtensionHost =
  typeof window !== 'undefined' &&
  typeof (window as unknown as { extensionsAPI?: unknown }).extensionsAPI !== 'undefined';

// Ensure SPA routers listening to popstate are notified after imperative navigation
const originalPush = browserHistory.push.bind(browserHistory);
browserHistory.push = (to, state) => {
  originalPush(to, state);
  try {
    window.dispatchEvent(new PopStateEvent('popstate'));
  } catch {
    // ignore
  }
};
const originalReplace = browserHistory.replace.bind(browserHistory);
browserHistory.replace = (to, state) => {
  originalReplace(to, state);
  if (!isExtensionHost) {
    try {
      window.dispatchEvent(new PopStateEvent('popstate'));
    } catch {
      // ignore
    }
  }
};

// Also patch global history so navigations not using browserHistory still notify listeners
(() => {
  if (typeof window === 'undefined' || !window.history) return;
  const originalWindowPush = window.history.pushState;
  const originalWindowReplace = window.history.replaceState;

  try {
    const patchedPush: typeof window.history.pushState = function (
      ...args: Parameters<typeof originalWindowPush>
    ) {
      const ret = originalWindowPush.apply(
        window.history,
        args as unknown as [unknown, string, string | URL | null | undefined]
      );
      // Always notify listeners after pushState, including in extension host
      try {
        window.dispatchEvent(new PopStateEvent('popstate'));
      } catch {
        // ignore
      }
      return ret;
    };
    window.history.pushState = patchedPush;
  } catch {
    // ignore
  }

  // Only dispatch popstate on replaceState outside of extension host to avoid loops
  if (!isExtensionHost) {
    try {
      const patchedReplace: typeof window.history.replaceState = function (
        ...args: Parameters<typeof originalWindowReplace>
      ) {
        const ret = originalWindowReplace.apply(
          window.history,
          args as unknown as [unknown, string, string | URL | null | undefined]
        );
        try {
          window.dispatchEvent(new PopStateEvent('popstate'));
        } catch {
          // ignore
        }
        return ret;
      };
      window.history.replaceState = patchedReplace;
    } catch {
      // ignore
    }
  }
})();
