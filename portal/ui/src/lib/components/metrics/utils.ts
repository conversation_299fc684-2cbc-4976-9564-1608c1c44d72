import gitUrlParse, { GitUrl } from 'git-url-parse';
import moment from 'moment';
import { useEffect } from 'react';
import { useSearchParams } from 'react-router-dom';

import { GroupByInterval } from '@ui/lib/apiclient/generated';
import { momentParse } from '@ui/lib/timezone';

// From milliseconds
export const SECOND = 1000;
export const MINUTE = 1000 * 60;
export const HOUR = 1000 * 60 * 60;
export const DAY = HOUR * 24;
export const MONTH = DAY * 30;
export const YEAR = MONTH * 12;

export const generatePossibleIntervals = (gapInMilliseconds: number): Array<GroupByInterval> => {
  const intervals: Array<GroupByInterval> = [];

  if (gapInMilliseconds <= 3 * DAY) {
    intervals.push(GroupByInterval.GROUP_BY_INTERVAL_MINUTE);
    intervals.push(GroupByInterval.GROUP_BY_INTERVAL_HOUR);
  }

  if (gapInMilliseconds <= 3 * MONTH && gapInMilliseconds >= DAY) {
    intervals.push(GroupByInterval.GROUP_BY_INTERVAL_DAY);
  }

  if (gapInMilliseconds <= YEAR && gapInMilliseconds >= 7 * DAY) {
    intervals.push(GroupByInterval.GROUP_BY_INTERVAL_WEEK);
  }

  if (gapInMilliseconds <= 3 * YEAR && gapInMilliseconds >= MONTH) {
    intervals.push(GroupByInterval.GROUP_BY_INTERVAL_MONTH);
  }

  if (gapInMilliseconds >= YEAR) {
    intervals.push(GroupByInterval.GROUP_BY_INTERVAL_YEAR);
  }

  return intervals;
};

export const isSHA = (revision: string) => {
  // https://stackoverflow.com/questions/468370/a-regex-to-match-a-sha1
  return revision.match(/^[a-f0-9]{5,40}$/) !== null;
};

export const parseRepoUrl = (url: string) => {
  try {
    const parsed = gitUrlParse(url);

    if (parsed.protocol === 'https') {
      return parsed.href;
    }

    const sshUrl = new URL(`https://${parsed.resource}${parsed.pathname}`);

    return sshUrl.toString();
  } catch {
    return '';
  }
};
function supportedSource(parsed: GitUrl): boolean {
  return (
    parsed.resource.startsWith('github') ||
    ['gitlab.com', 'bitbucket.org'].indexOf(parsed.source) >= 0
  );
}

function protocol(proto: string): string {
  return proto === 'ssh' ? 'https' : proto;
}

export function revisionUrl(url: string, revision: string, forPath = false): string {
  let parsed;
  try {
    parsed = gitUrlParse(url);
  } catch {
    return null;
  }
  let urlSubPath = isSHA(revision) ? 'commit' : 'tree';

  if (url.indexOf('bitbucket') >= 0) {
    // The reason for the condition of 'forPath' is that when we build nested path, we need to use 'src'
    urlSubPath = isSHA(revision) && !forPath ? 'commits' : 'src';
  }

  // Gitlab changed the way urls to commit look like
  // Ref: https://docs.gitlab.com/ee/update/deprecations.html#legacy-urls-replaced-or-removed
  if (parsed.source === 'gitlab.com') {
    urlSubPath = '-/' + urlSubPath;
  }

  if (!supportedSource(parsed)) {
    return null;
  }

  return `${protocol(parsed.protocol)}://${parsed.resource}/${parsed.owner}/${parsed.name}/${urlSubPath}/${revision || 'HEAD'}`;
}

export const formatDateForGraph = (interval: GroupByInterval, date: moment.Moment): string => {
  switch (interval) {
    case GroupByInterval.GROUP_BY_INTERVAL_MINUTE:
      return date.format(`h:mmA MMM Do [']YY`);
    case GroupByInterval.GROUP_BY_INTERVAL_HOUR:
      return date.format(`hA MMM Do [']YY`);
    case GroupByInterval.GROUP_BY_INTERVAL_DAY:
      return date.format(`MMM Do [']YY`);
    case GroupByInterval.GROUP_BY_INTERVAL_WEEK: {
      const oneWeek = date.clone().add(1, 'week');
      return date.format('MMM') + ' ' + date.format('Do') + '-' + oneWeek.format("Do [']YY");
    }
    case GroupByInterval.GROUP_BY_INTERVAL_MONTH:
      return date.format(`MMM [']YY`);
    case GroupByInterval.GROUP_BY_INTERVAL_YEAR:
      return date.format('YYYY');
  }
};

export type availableTimeRangesKey =
  | '1-h'
  | '24-h'
  | '3-d'
  | '7-d'
  | '14-d'
  | '30-d'
  | '3-m'
  | '6-m'
  | '1-y';

export const availableTimeRanges: Array<{
  getRange: () => [Date, Date];
  label: string;
  key: availableTimeRangesKey;
}> = [
  {
    getRange: () => [moment().subtract(1, 'h').toDate(), new Date()],
    label: '1 Hour',
    key: '1-h'
  },
  {
    getRange: () => [moment().subtract(24, 'h').toDate(), new Date()],
    label: '24 Hours',
    key: '24-h'
  },
  {
    getRange: () => [moment().subtract(3, 'd').toDate(), new Date()],
    label: '3 Days',
    key: '3-d'
  },
  {
    getRange: () => [moment().subtract(7, 'd').toDate(), new Date()],
    label: '7 Days',
    key: '7-d'
  },
  {
    getRange: () => [moment().subtract(14, 'd').toDate(), new Date()],
    label: '14 Days',
    key: '14-d'
  },
  {
    getRange: () => [moment().subtract(30, 'd').toDate(), new Date()],
    label: '30 Days',
    key: '30-d'
  },
  {
    getRange: () => [moment().subtract(3, 'months').toDate(), new Date()],
    label: '3 Months',
    key: '3-m'
  },
  {
    getRange: () => [moment().subtract(6, 'months').toDate(), new Date()],
    label: '6 Months',
    key: '6-m'
  },
  {
    getRange: () => [moment().subtract(1, 'year').toDate(), new Date()],
    label: 'Year',
    key: '1-y'
  }
];

export const useDefinedTimerange = () => {
  const [search, overwriteSearch] = useSearchParams();

  return {
    definedTimerange: search.get('definedTimerange') || '7-d',
    setDefinedTimerange: (t: availableTimeRangesKey | '') => {
      overwriteSearch((prevSearch) => {
        const existingSearch = new URLSearchParams(prevSearch);

        existingSearch.set('definedTimerange', t);

        const range = availableTimeRanges.find((r) => r.key === t)?.getRange() || [];

        if (range?.length > 0) {
          existingSearch.delete('timerange');
        }

        for (const r of range) {
          existingSearch.append('timerange', r.toISOString());
        }

        existingSearch.set(
          'interval',
          getSafeIntervalByTimerange(range, existingSearch.get('interval') as GroupByInterval)
        );

        return existingSearch;
      });
    }
  };
};

// interval saftey
// ie. timerange for year gap does not support interval by minute
export const getSafeIntervalByTimerange = (timerange: Date[], currentInterval: GroupByInterval) => {
  const start = timerange[0];
  const end = timerange[1];

  if (start && end) {
    const possibleIntervals = generatePossibleIntervals(end.getTime() - start.getTime());

    if (!possibleIntervals.includes(currentInterval)) {
      return possibleIntervals[0];
    }
  }

  return currentInterval;
};

// this maps with defined timerange ie. 1 hour, 1 month form of text
// based on selected timerange
export const getDefinedTimerange = (timerange: Date[]): string => {
  const definedTimerange = availableTimeRanges.find(({ getRange }) => {
    const getGeneratedRange = getRange();

    const secondPrecise = (time: number) => Math.ceil(time / 1000);

    if (
      secondPrecise(timerange?.[0]?.getTime?.()) ===
        secondPrecise(getGeneratedRange?.[0]?.getTime?.()) &&
      secondPrecise(timerange?.[1]?.getTime?.()) ===
        secondPrecise(getGeneratedRange?.[1]?.getTime?.())
    ) {
      return true;
    }

    return false;
  });

  if (definedTimerange) {
    return definedTimerange.label;
  }

  return 'Custom';
};

export const suggestInputAsOption = (
  existingSuggestions: Array<{ label: string | React.ReactNode; value: string }>,
  search: string,
  empty?: boolean
): Array<{
  label: string | React.ReactNode;
  value: string;
}> => {
  if (empty) {
    return [];
  }

  if (search === '') {
    return existingSuggestions;
  }

  if (!existingSuggestions.find((s) => s.value === search)) {
    return [...existingSuggestions, { label: search, value: search }];
  }

  return existingSuggestions;
};

export const humanReadableInterval = (interval: GroupByInterval) => {
  switch (interval) {
    case GroupByInterval.GROUP_BY_INTERVAL_DAY:
      return 'Day';
    case GroupByInterval.GROUP_BY_INTERVAL_HOUR:
      return 'Hour';
    case GroupByInterval.GROUP_BY_INTERVAL_MONTH:
      return 'Month';
    case GroupByInterval.GROUP_BY_INTERVAL_WEEK:
      return 'Week';
    case GroupByInterval.GROUP_BY_INTERVAL_YEAR:
      return 'Year';
    case GroupByInterval.GROUP_BY_INTERVAL_MINUTE:
      return 'Minute';
    default:
      return 'None';
  }
};

export const unitForInterval = (interval: GroupByInterval) => {
  switch (interval) {
    case GroupByInterval.GROUP_BY_INTERVAL_HOUR:
      return 'hours';
    case GroupByInterval.GROUP_BY_INTERVAL_DAY:
      return 'days';
    case GroupByInterval.GROUP_BY_INTERVAL_WEEK:
      return 'weeks';
    case GroupByInterval.GROUP_BY_INTERVAL_MONTH:
      return 'months';
    case GroupByInterval.GROUP_BY_INTERVAL_YEAR:
      return 'years';
    case GroupByInterval.GROUP_BY_INTERVAL_MINUTE:
      return 'minutes';
    default:
      return 'days';
  }
};

export const formattedTimestamp = (timestamp: string) => {
  return momentParse.withGlobalFormat(timestamp).format();
};

export const useFilterSearchParams = (searchStr: string, enabled?: boolean) => {
  useEffect(() => {
    if (!enabled) {
      return;
    }

    const currentSearch = new URLSearchParams(window?.location?.search);

    currentSearch.set('filters', searchStr);

    window.history.replaceState({}, '', `?${currentSearch.toString()}`);
  }, [searchStr]);
};

export const copyReportLink = async (link: string) => {
  const currentURL = new URL(window.location.href);

  const currentURLSearch = currentURL.searchParams;

  currentURLSearch.set('filters', link);

  await navigator.clipboard.writeText(currentURL.toString());
};

export const MetricsTheme = {
  success: 'rgba(17, 189, 106, 1)',
  fail: 'rgba(251, 86, 86, 1)',
  randoms: [
    '#7765E3',
    '#FF6B6B',
    '#048A81',
    '#3581B8',
    '#FCB07E',
    '#0081AF',
    '#3B60E4',
    '#FF7700',
    '#00AFB5',
    '#29524A',
    '#D81E5B',
    '#A3320B',
    '#6A381F',
    '#04773B',
    '#EC0B43'
  ]
};

export class RandomColors {
  private used: Map<number, boolean>;
  private store: Map<string, string>;
  private colorToUse: number;

  constructor(seed?: number) {
    this.used = new Map();
    this.store = new Map();
    this.colorToUse = seed || 0;
  }

  use() {
    const color = this.colorToUse;

    this.colorToUse = (this.colorToUse + 1) % MetricsTheme.randoms.length;

    this.used.set(color, true);

    return MetricsTheme.randoms[color];
  }

  reuse(key: string) {
    if (!this.store.has(key)) {
      this.store.set(key, this.use());
    }

    return this.store.get(key);
  }
}
