import { PlainMessage } from '@bufbuild/protobuf';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { useQuery } from '@tanstack/react-query';

import { usePlatformApiFetch } from '@ui/feature/shared/hooks/use-platform-api-fetch';
import { usePlatformApiWatch } from '@ui/feature/shared/hooks/use-platform-api-watch';
import { useAISupportEngineerContext } from '@ui/lib/context/ai-support-engineer-context';
import { ExtendedAIMessageContext, UseQueryOptionsOverride } from '@ui/lib/types';

import { GetInstanceResponse, ListInstancesResponse } from '../argocd/v1/argocd_pb';
import { queryKeys } from '../query-keys';
import { ApiError } from '../use-api-fetch';
import { getQueryParamsAsString } from '../utils';

import {
  CreateAIConversationResponse,
  CreateAIConversationRequest,
  GetAIConversationResponse,
  ListAIConversationsResponse,
  CreateAIMessageRequest,
  CreateAIMessageResponse,
  EnabledCluster,
  ListKubernetesEnabledClustersRequest,
  ListKubernetesEnabledClustersResponse,
  ListAIConversationSuggestionsRequest,
  ListAIConversationSuggestionsResponse,
  UpdateAIConversationRequest,
  UpdateAIConversationResponse,
  DeleteAIConversationRequest,
  DeleteAIConversationResponse,
  UpdateAIMessageFeedbackRequest,
  UpdateAIMessageFeedbackResponse,
  GetAIConversationStreamResponse,
  AIMessage,
  UpdateAIConversationFeedbackRequest,
  UpdateAIConversationFeedbackResponse,
  ListAIConversationsRequest
} from './v1/organization_pb';

export const useGetKubernetesEnabledClustersQuery = (
  data: Omit<
    PlainMessage<ListKubernetesEnabledClustersRequest> & { includeDisabled?: boolean },
    'organizationId'
  >,
  opts?: UseQueryOptionsOverride<EnabledCluster[], readonly string[]>
) => {
  const { organizationId } = useAISupportEngineerContext();
  const apiFetch = usePlatformApiFetch();

  const params = getQueryParamsAsString(data);

  return useQuery({
    queryKey: queryKeys.kubevision.clusters(organizationId, params).queryKey,
    queryFn: () => {
      return apiFetch(`orgs/${organizationId}/k8s/clusters?${params}`)
        .then(ListKubernetesEnabledClustersResponse.fromJson)
        .then((res) => res.clusters.filter((c) => data.includeDisabled || c.isEnabled));
    },
    ...opts
  });
};

export const useGetAIConversationQuery = (
  id: string,
  opts?: UseQueryOptionsOverride<GetAIConversationResponse, readonly string[]>
) => {
  const { organizationId, instanceId } = useAISupportEngineerContext();
  const apiFetch = usePlatformApiFetch();

  return useQuery({
    queryKey: queryKeys.ai.getConversation(organizationId, instanceId, id).queryKey,
    queryFn: () => {
      return apiFetch(
        `orgs/${organizationId}/ai/conversations/${id}?instanceId=${instanceId}`,
        undefined,
        { hideErrorNotification: true }
      ).then(GetAIConversationResponse.fromJson);
    },
    ...opts
  });
};

export const useWatchAIConversation = (id: string, opts: { enabled: boolean }) => {
  const queryClient = useQueryClient();
  const { organizationId, instanceId } = useAISupportEngineerContext();

  const newConversationContexts = queryClient.getQueryData(
    queryKeys.ai.newConversationContexts(organizationId, instanceId, id).queryKey
  ) as ExtendedAIMessageContext[];

  usePlatformApiWatch({
    resource: `stream/orgs/${organizationId}/ai/conversations/${id}/messages?instanceId=${instanceId}`,
    enabled: opts.enabled,
    onMessage: (data) => {
      const result = GetAIConversationStreamResponse.fromJson(data);

      queryClient.setQueryData(
        queryKeys.ai.watchConversation(organizationId, instanceId, id).queryKey,
        (oldData: GetAIConversationResponse): GetAIConversationResponse => {
          if (!oldData || !oldData.conversation) {
            if (newConversationContexts) {
              queryClient.removeQueries({
                queryKey: queryKeys.ai.newConversationContexts(organizationId, instanceId, id)
                  .queryKey
              });
              return new GetAIConversationResponse({
                ...result,
                conversation: {
                  ...result.conversation,
                  contexts: newConversationContexts
                }
              });
            }
            return result.conversation
              ? new GetAIConversationResponse(result)
              : new GetAIConversationResponse();
          }

          const existingMessages = new Map<string, AIMessage>();
          oldData.conversation.messages.forEach((item) => existingMessages.set(item.id, item));
          result.conversation?.messages.forEach((item) => existingMessages.set(item.id, item));
          const messages = Array.from(existingMessages.values()).sort(
            (a, b) => a.createTime.toDate().getTime() - b.createTime.toDate().getTime()
          );

          const updatedConversationProperties = {
            ...oldData.conversation,
            ...(result.conversation || {}),
            incident: result.conversation?.incident
          };

          const conversation = { ...updatedConversationProperties, messages };
          return new GetAIConversationResponse({ ...oldData, conversation });
        }
      );
    }
  });

  return useQuery({
    queryKey: queryKeys.ai.watchConversation(organizationId, instanceId, id).queryKey,
    queryFn: () => {
      const data = queryClient.getQueryData(
        queryKeys.ai.watchConversation(organizationId, instanceId, id).queryKey
      ) as GetAIConversationResponse | undefined;
      return data || new GetAIConversationResponse();
    }
  });
};

export const useListAIConversationsQuery = (
  data: Omit<PlainMessage<ListAIConversationsRequest>, 'organizationId' | 'instanceId'>,
  opts?: UseQueryOptionsOverride<ListAIConversationsResponse, readonly string[]>
) => {
  const { organizationId, instanceId } = useAISupportEngineerContext();
  const apiFetch = usePlatformApiFetch();
  const params = getQueryParamsAsString(data);

  return useQuery({
    queryKey: queryKeys.ai.conversations(organizationId, instanceId, params).queryKey,
    queryFn: () => {
      return apiFetch(
        `orgs/${organizationId}/ai/conversations?instanceId=${instanceId}&${params}`
      ).then(ListAIConversationsResponse.fromJson);
    },
    ...opts
  });
};

export const useCreateAIConversationMutation = () => {
  const apiFetch = usePlatformApiFetch();

  return useMutation<
    CreateAIConversationResponse,
    ApiError,
    PlainMessage<CreateAIConversationRequest>
  >({
    mutationFn: ({ organizationId, ...data }) =>
      apiFetch(`orgs/${organizationId}/ai/conversations`, {
        method: 'POST',
        body: JSON.stringify(data)
      }).then(CreateAIConversationResponse.fromJson)
  });
};

export const useCreateAIMessageMutation = () => {
  const apiFetch = usePlatformApiFetch();

  return useMutation<CreateAIMessageResponse, ApiError, PlainMessage<CreateAIMessageRequest>>({
    mutationFn: ({ organizationId, conversationId, ...data }) =>
      apiFetch(`orgs/${organizationId}/ai/conversations/${conversationId}/messages`, {
        method: 'POST',
        body: JSON.stringify(data)
      }).then(CreateAIMessageResponse.fromJson)
  });
};

export const useListAIConversationSuggestionsQuery = (
  data: Omit<PlainMessage<ListAIConversationSuggestionsRequest>, 'organizationId'>,
  opts?: UseQueryOptionsOverride<ListAIConversationSuggestionsResponse, readonly string[]>
) => {
  const { organizationId } = useAISupportEngineerContext();
  const apiFetch = usePlatformApiFetch();

  const { conversationId, ...contexts } = data;

  const params = getQueryParamsAsString(contexts);

  return useQuery({
    queryKey: queryKeys.ai.suggestions(organizationId, conversationId, params).queryKey,
    queryFn: () => {
      return apiFetch(`orgs/${organizationId}/ai/conversations/${conversationId}/suggestions`, {
        method: 'POST',
        body: JSON.stringify(contexts)
      }).then(ListAIConversationSuggestionsResponse.fromJson);
    },
    ...opts
  });
};

export const useUpdateAIConversationMutation = () => {
  const { organizationId, instanceId } = useAISupportEngineerContext();
  const apiFetch = usePlatformApiFetch();

  return useMutation({
    mutationFn: (data: Omit<PlainMessage<UpdateAIConversationRequest>, 'organizationId'>) => {
      const { id, ...context } = data;

      return apiFetch(`orgs/${organizationId}/ai/conversations/${id}`, {
        method: 'PUT',
        body: JSON.stringify({ ...context, instanceId })
      }).then(UpdateAIConversationResponse.fromJson);
    }
  });
};

export const useUpdateAIConversationFeedbackMutation = () => {
  const { organizationId, instanceId } = useAISupportEngineerContext();
  const apiFetch = usePlatformApiFetch();

  return useMutation({
    mutationFn: (
      data: Omit<PlainMessage<UpdateAIConversationFeedbackRequest>, 'organizationId'>
    ) => {
      const { conversationId, feedback } = data;

      return apiFetch(
        `orgs/${organizationId}/ai/conversations/${conversationId}/conversation-feedback`,
        {
          method: 'PATCH',
          body: JSON.stringify({ feedback, instanceId })
        }
      ).then(UpdateAIConversationFeedbackResponse.fromJson);
    }
  });
};

export const useDeleteAIConversationMutation = () => {
  const { organizationId, instanceId } = useAISupportEngineerContext();
  const apiFetch = usePlatformApiFetch();

  return useMutation({
    mutationFn: (data: Omit<PlainMessage<DeleteAIConversationRequest>, 'organizationId'>) => {
      const { id } = data;

      return apiFetch(`orgs/${organizationId}/ai/conversations/${id}?instanceId=${instanceId}`, {
        method: 'DELETE'
      }).then(DeleteAIConversationResponse.fromJson);
    }
  });
};

export const useUpdateAIMessageFeedbackMutation = () => {
  const { organizationId, instanceId } = useAISupportEngineerContext();
  const apiFetch = usePlatformApiFetch();

  return useMutation({
    mutationFn: (data: Omit<PlainMessage<UpdateAIMessageFeedbackRequest>, 'organizationId'>) => {
      const { conversationId, ...context } = data;

      return apiFetch(`orgs/${organizationId}/ai/conversations/${conversationId}/feedback`, {
        method: 'PATCH',
        body: JSON.stringify({ ...context, instanceId })
      }).then(UpdateAIMessageFeedbackResponse.fromJson);
    }
  });
};

export const useUpdateIncidentMutation = () => {
  const { organizationId } = useAISupportEngineerContext();
  const apiFetch = usePlatformApiFetch();

  return useMutation({
    mutationFn: (incident: UpdateAIConversationRequest) => {
      return apiFetch(`orgs/${organizationId}/ai/conversations/${incident.id}`, {
        method: 'PUT',
        body: JSON.stringify(incident.toJson())
      });
    }
  });
};

export const useListInstances = (
  opts?: UseQueryOptionsOverride<ListInstancesResponse, readonly string[]>
) => {
  const { organizationId } = useAISupportEngineerContext();
  const apiFetch = usePlatformApiFetch();

  return useQuery({
    queryKey: queryKeys.kubevision.instances(organizationId).queryKey,
    queryFn: () =>
      apiFetch(`orgs/${organizationId}/argocd/instances`).then(ListInstancesResponse.fromJson),
    ...opts
  });
};

export const useGetInstanceQuery = (
  instanceId: string,
  opts?: UseQueryOptionsOverride<GetInstanceResponse, readonly string[]>
) => {
  const { organizationId } = useAISupportEngineerContext();
  const apiFetch = usePlatformApiFetch();

  return useQuery({
    queryKey: queryKeys.kubevision.instance(organizationId, instanceId).queryKey,
    queryFn: () => {
      return apiFetch(`orgs/${organizationId}/argocd/instances/${instanceId}`).then(
        GetInstanceResponse.fromJson
      );
    },
    ...opts
  });
};
