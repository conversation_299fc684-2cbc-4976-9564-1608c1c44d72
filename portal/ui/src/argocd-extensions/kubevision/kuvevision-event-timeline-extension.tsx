import { faInfoCircle } from '@fortawesome/free-solid-svg-icons';
import { useQuery } from '@tanstack/react-query';
import { Flex } from 'antd';

import { KubeVisionEventTimeline } from '@ui/feature/kubevision/components/shared/kubevision-event-timeline';
import { UTCTimeContextProvider } from '@ui/feature/kubevision/components/shared/utctime-context';
import {
  akpTextPrimaryColor,
  featureNotEnabledDescription,
  featureExtensionNotAllowed
} from '@ui/feature/kubevision/const';
import { PlatformContextProvider } from '@ui/feature/shared/context/platform-context-provider';
import { IconLabel, Loading } from '@ui/lib/components';
import {
  KubeVisionContextProvider,
  useKubeVisionContext
} from '@ui/lib/context/kubevision-context';

import { Application, Resource } from '../types';

type ContextedKubeVisionEventTimelineProps = {
  resourceKind: string;
  instanceId: string;
  application: Application;
  resourceGroup: string;
  resourceApplicationName: string;
  resourceNamespace: string;
  resourceName: string;
};

const ContextedKubeVisionEventTimeline = ({
  resourceKind,
  instanceId,
  application,
  resourceGroup,
  resourceApplicationName,
  resourceNamespace,
  resourceName
}: ContextedKubeVisionEventTimelineProps) => {
  const { enabledClustersInfo } = useKubeVisionContext();
  const clusterId = enabledClustersInfo.clusterId({
    instanceId,
    clusterName: application?.metadata?.labels?.cluster || ''
  });
  return (
    <KubeVisionEventTimeline
      scope={resourceKind === 'Namespace' ? 'namespace' : 'resource'}
      instanceId={instanceId}
      clusterId={clusterId}
      applicationNames={
        resourceGroup === 'argoproj.io' && resourceKind === 'Application' && resourceApplicationName
          ? [resourceApplicationName]
          : []
      }
      namespace={resourceKind === 'Namespace' ? resourceName : resourceNamespace}
      name={resourceName}
      group={resourceGroup}
      kind={resourceKind}
    />
  );
};

type Props = {
  application: Application;
  resource: Resource;
  organizationId: string;
  instanceId: string;
  allowedUsernames: Array<string>;
  allowedGroups: Array<string>;
  isk8sFeatureOn: boolean;
};

export const KubeVisionEventTimelineExtension = ({
  application,
  resource,
  organizationId,
  instanceId,
  allowedUsernames,
  allowedGroups,
  isk8sFeatureOn
}: Props) => {
  const { data, isFetching } = useQuery({
    queryKey: ['argo', 'session', 'userinfo'],
    queryFn: async () => {
      const resp = await fetch('/api/v1/session/userinfo');
      return resp.json();
    }
  });

  const username = data?.username || '';
  const groups = data?.groups || [];

  let isAllowed = false;
  for (const allowedUsername of allowedUsernames) {
    if (allowedUsername === username || allowedUsername === '*') {
      isAllowed = true;
      break;
    }
  }
  for (const allowedGroup of allowedGroups) {
    for (const group of groups) {
      if (allowedGroup === group || allowedGroup === '*') {
        isAllowed = true;
        break;
      }
    }
  }

  if (!isk8sFeatureOn) {
    return (
      <div className='mx-auto my-40 px-20 py-10 w-[65%] rounded-2xl bg-[#fafafa]'>
        <IconLabel
          icon={faInfoCircle}
          iconSize={'xl'}
          iconClass='mr-8'
          className='text-lg leading-loose'
          label={featureNotEnabledDescription}
        />
      </div>
    );
  }

  if (isFetching) {
    return (
      <Flex justify={'center'} className='w-full mt-20' style={{ color: akpTextPrimaryColor }}>
        <Loading />
      </Flex>
    );
  }

  if (!isAllowed) {
    return (
      <div className='mx-auto my-40 px-20 py-10 w-[65%] rounded-2xl text-[#495763] bg-[#fafafa]'>
        <IconLabel
          icon={faInfoCircle}
          iconSize={'xl'}
          iconClass='mr-8'
          className='text-lg leading-loose'
          label={featureExtensionNotAllowed}
        />
      </div>
    );
  }

  const resourceApplicationName = application?.metadata?.name || '';
  const resourceNamespace = resource?.metadata?.namespace || '';
  const resourceName = resource?.metadata?.name || '';
  const resourceGroup = resource?.apiVersion?.split('/')?.[0] || 'argoproj.io';
  const resourceKind = resource?.kind || 'Application';

  return (
    <PlatformContextProvider platform='argocd'>
      <UTCTimeContextProvider>
        <KubeVisionContextProvider
          organizationMode={false}
          organizationId={organizationId}
          instanceId={instanceId}
        >
          <ContextedKubeVisionEventTimeline
            resourceKind={resourceKind}
            instanceId={instanceId}
            application={application}
            resourceGroup={resourceGroup}
            resourceApplicationName={resourceApplicationName}
            resourceNamespace={resourceNamespace}
            resourceName={resourceName}
          />
        </KubeVisionContextProvider>
      </UTCTimeContextProvider>
    </PlatformContextProvider>
  );
};
