import { mountAISupportEngineer } from './kargo-extensions/akuity-intelligence/ai-support-engineer';
import { mountKubeVision } from './kargo-extensions/akuity-intelligence/kubevision';
import { KargoSettings } from './lib/apiclient/extension/v1/extension_pb';
import './kargo-extensions.less';
import { browserHistory } from './lib/history';

(() => {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const settings = KargoSettings.fromJson((window as any).__akuity?.Settings ?? {});
  mountKubeVision(settings, browserHistory);
  mountAISupportEngineer(settings, browserHistory);
})();
