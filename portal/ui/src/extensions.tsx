import * as Sentry from '@sentry/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import {
  Chart as ChartJS,
  ArcElement,
  Tooltip,
  Legend,
  CategoryScale,
  LinearScale,
  TimeScale,
  BarElement,
  PointElement,
  LineElement
} from 'chart.js';
import { TreemapController, TreemapElement } from 'chartjs-chart-treemap';
import zoomPlugin from 'chartjs-plugin-zoom';
import * as React from 'react';
import { useCallback, useEffect } from 'react';
import * as ReactDOM from 'react-dom';
import type * as ReactDOMClient from 'react-dom/client';
import { unstable_HistoryRouter as Router } from 'react-router-dom';
import { lt as semverLessThan } from 'semver';
import { useSyncExternalStore } from 'use-sync-external-store/shim';

import '@ui/extensions.less';

import { KubeVisionExtension } from '@ui/argocd-extensions/kubevision/kubevision-extension';
import { isMinimumInstanceVersionSatisfied } from '@ui/feature/kubevision/version';
import { Loading } from '@ui/lib/components';

import { Application, Resource } from './argocd-extensions';
import { AISupportEngineerExtension } from './argocd-extensions/ai-support-engineer/ai-support-engineer-extension';
import { ApplicationSetExtension } from './argocd-extensions/applicationset/applicationset-extension';
import { ApplicationSetSystemExtension } from './argocd-extensions/applicationset/applicationset-system-extension';
import { CrossplaneExtension } from './argocd-extensions/crossplane/crossplane-extension';
import { ErrorFallback } from './argocd-extensions/error-fallback';
import { KubeVisionStatusPanelButtons } from './argocd-extensions/kubevision/kubevision-status-panel-buttons';
import { KubeVisionEventTimelineExtension } from './argocd-extensions/kubevision/kuvevision-event-timeline-extension';
import { sentryUtils } from './argocd-extensions/utils';
import { eventTimelineEnabledGroups } from './feature/kubevision/components/shared/kubevision-event-timeline/helper';
import { Settings } from './lib/apiclient/extension/v1/extension_pb';
import { FeatureStatus } from './lib/apiclient/types/features/v1/features_pb';
import { NotificationContextProvider } from './lib/context/notification-context';
import { browserHistory } from './lib/history';

ChartJS.defaults.font.family = 'sans-serif';

ChartJS.register(
  ArcElement,
  Tooltip,
  Legend,
  CategoryScale,
  LinearScale,
  TimeScale,
  BarElement,
  Legend,
  PointElement,
  LineElement,
  zoomPlugin,
  TreemapController,
  TreemapElement
);

const replaceState = history.replaceState;

/**
 * https://github.com/akuityio/akuity-platform/issues/3504
 *
 * react-query starting from version 5 requires minimum React version 18
 *
 * if this external version (coming from argocd) is less then that then we need to patch the new method that is used by react-query and is in React 18
 */
if (semverLessThan(React.version, '18.0.0')) {
  // shim provided by react
  // from the migration docs - https://tanstack.com/query/v5/docs/react/guides/migrating-to-v5#the-minimum-required-react-version-is-now-180
  /**
   * "React Query v5 requires React 18.0 or later. This is because we are using the new useSyncExternalStore hook, which is only available in React 18.0 and later. Previously, we have been using the shim provided by React."
   * we will use the same shim - https://github.com/reactwg/react-18/discussions/86
   */
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  (React as any).useSyncExternalStore = useSyncExternalStore;
}

interface AkuityAIRoot {
  root?: ReactDOMClient.Root;
  container: HTMLDivElement;
}

declare global {
  interface Window {
    __akuityAIRoot?: AkuityAIRoot;
  }
}

interface CreateRootFn {
  createRoot?: (container: Element | DocumentFragment) => ReactDOMClient.Root;
}

const useCreateRoot = !!(ReactDOM as unknown as CreateRootFn).createRoot;

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: false,
      refetchOnWindowFocus: false,
      gcTime: 0
    }
  }
});

const extensionComponentContainer = (
  Component: React.FunctionComponent<{
    application: Application;
    resource: Resource;
    settings: Settings;
  }>,
  settings: Settings
) => {
  return (props: { application: Application; resource: Resource }) => {
    return (
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      <Router history={browserHistory as any}>
        <Sentry.ErrorBoundary
          beforeCapture={sentryUtils.addAkpExtensionContext}
          fallback={<ErrorFallback />}
        >
          <NotificationContextProvider>
            <QueryClientProvider client={queryClient}>
              <Component {...props} settings={settings} />
            </QueryClientProvider>
          </NotificationContextProvider>
        </Sentry.ErrorBoundary>
      </Router>
    );
  };
};

const AuditExtensionLazy = React.lazy(() =>
  import('./argocd-extensions/audit/audit-extension').then((def) => ({
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    default: (props: any) => <def.AuditExtension {...props} />
  }))
);

// eslint-disable-next-line @typescript-eslint/no-explicit-any
const AuditExtension = (props: any) => (
  <React.Suspense fallback={<Loading />}>
    <AuditExtensionLazy {...props} />
  </React.Suspense>
);

const SyncHistoryExtensionLazy = React.lazy(() =>
  import('./argocd-extensions/sync-history/sync-history-extension')
    .then((def) => def.SyncHistoryExtension)
    .then((SyncHistoryExtension) => ({
      default: SyncHistoryExtension
    }))
);

// eslint-disable-next-line @typescript-eslint/no-explicit-any
const SyncHistoryExtension = (props: any) => (
  <React.Suspense fallback={<Loading />}>
    <SyncHistoryExtensionLazy {...props} />
  </React.Suspense>
);

const AssistantExtensionLazy = React.lazy(() =>
  import('./argocd-extensions/assistant/assistant-extension').then((def) => ({
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    default: (props: any) => <def.AssistantExtension {...props} />
  }))
);

// eslint-disable-next-line @typescript-eslint/no-explicit-any
const AssistantExtension = (props: any) => (
  <React.Suspense fallback={<Loading />}>
    <AssistantExtensionLazy {...props} />
  </React.Suspense>
);

const AISupportEngineerListener = (params: { application: Application }) => {
  const { application } = params;

  const getResourceFromUrl = useCallback(
    (url: string): Resource | null => {
      const absUrl =
        url.startsWith('http:') || url.startsWith('https:') ? url : window.location.origin + url;
      const urlObj = new URL(absUrl);
      const params = new URLSearchParams(urlObj.search);
      const node = params.get('node');
      const chunks = node?.split('/');
      const group = chunks?.[0] || '';
      const kind = chunks?.[1] || '';
      const namespace = chunks?.[2] || '';
      const name = chunks?.[3] || '';
      const resource = application?.status?.resources?.find((r) => {
        const rgroup = r?.group || '';
        const rkind = r?.kind || '';
        const rnamespace = r?.namespace || '';
        const rname = r?.name || '';
        return rgroup === group && rkind === kind && rnamespace === namespace && rname === name;
      });
      if (!resource) {
        return null;
      }
      return {
        apiVersion: `${resource.group}/${resource.version}`,
        kind: resource.kind,
        metadata: {
          uid: '',
          name: resource.name,
          namespace: resource.namespace
        }
      };
    },
    [application]
  );

  const notifyApplicationAndResource = useCallback(
    (application: Application, resource: Resource) => {
      window.dispatchEvent(
        new CustomEvent('AISupportEngineerExtension/onApplication', {
          detail: { application, resource }
        })
      );
    },
    []
  );

  useEffect(() => {
    history.replaceState = function (...args: Parameters<typeof replaceState>) {
      // replaceState(state, unused, url), url(args[2]) is optional, so we need to check if it exists
      // and it maybe an full url with window.location.origin, we need to keep it unified
      const fullUrl = new URL(args?.[2]?.toString() || '', window.location.href).href;
      const resource = getResourceFromUrl(fullUrl);

      notifyApplicationAndResource(application, resource);
      return replaceState.apply(history, args);
    };

    const resource = getResourceFromUrl(window.location.href);
    notifyApplicationAndResource(application, resource);
    return () => {
      history.replaceState = replaceState;
      notifyApplicationAndResource(null, null);
    };
  }, [application]);
  return <></>;
};

// eslint-disable-next-line @typescript-eslint/no-explicit-any
(async (window: any) => {
  let settings: Settings;
  try {
    // workaround for argocd behavior: restore akuity query parameters
    if (window['__akuity__openURL']) {
      const openURL = window['__akuity__openURL'] as string;
      const search = new URLSearchParams(window.location.search);
      const url = new URL(openURL);
      for (const [key, value] of url.searchParams.entries()) {
        if (key.startsWith('akuity-') && !search.has(key)) {
          search.set(key, value);
        }
      }
      window.history.replaceState(null, null, `${window.location.pathname}?${search.toString()}`);
    }
    settings = Settings.fromJson(window['__akuity']);
  } catch (error) {
    return;
  }

  if (!window.__SENTRY__ && settings?.config?.argocdExtensionSentryDsn) {
    Sentry.init({
      dsn: settings.config.argocdExtensionSentryDsn,
      environment: settings.config.env,
      release: __UI_VERSION__,
      beforeSend(event) {
        if (sentryUtils.isAkpExtensionContext(event)) {
          return sentryUtils.removeAkpExtensionContext(event);
        }
        return null;
      }
    });
  }

  if (settings.auditExtensionEnabled) {
    window.extensionsAPI.registerResourceExtension(
      extensionComponentContainer(AuditExtension, settings),
      'argoproj.io',
      'Application',
      'Audit Logs'
    );
  }

  if (settings.syncHistoryExtensionEnabled) {
    window.extensionsAPI.registerResourceExtension(
      extensionComponentContainer(SyncHistoryExtension, settings),
      'argoproj.io',
      'Application',
      'Sync History'
    );
  }

  if (settings.assistantExtensionEnabled) {
    window.extensionsAPI.registerResourceExtension(
      extensionComponentContainer(AssistantExtension, settings),
      '**',
      '*',
      'Assistant',
      {
        icon: 'fa fa-hat-wizard'
      }
    );
  }

  if ((settings.crossplaneExtension?.resources || []).length > 0) {
    for (const resource of settings.crossplaneExtension?.resources || []) {
      window.extensionsAPI.registerResourceExtension(
        extensionComponentContainer((props) => <CrossplaneExtension {...props} />, settings),
        resource.group,
        '*',
        'Crossplane'
      );
    }
  }

  if (
    isMinimumInstanceVersionSatisfied(settings.instanceVersion) &&
    settings.featureStatuses?.multiClusterK8sDashboard === FeatureStatus.ENABLED &&
    settings.akuityIntelligenceExtension?.enabled
  ) {
    window.extensionsAPI.registerSystemLevelExtension(
      extensionComponentContainer(
        () => (
          <KubeVisionExtension
            organizationId={settings?.organizationId}
            instanceId={settings?.instanceId}
            allowedUsernames={settings.akuityIntelligenceExtension?.allowedUsernames || []}
            allowedGroups={settings.akuityIntelligenceExtension?.allowedGroups || []}
            isk8sFeatureOn={settings.akuityIntelligenceExtension?.enabled}
            isAISREFeatureOn={
              settings.featureStatuses.aiSupportEngineer === FeatureStatus.ENABLED &&
              settings.akuityIntelligenceExtension?.enabled &&
              settings.akuityIntelligenceExtension?.aiSupportEngineerEnabled
            }
            kubeVisionConfig={settings.kubeVisionConfig}
          />
        ),
        settings
      ),
      'Intelligence (Beta)',
      '/kubevision',
      'fa-eye'
    );

    const statusPanelButton = (props: { openFlyout: () => void; application: Application }) => {
      return (
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        <Router history={browserHistory as any}>
          <QueryClientProvider client={queryClient}>
            <KubeVisionStatusPanelButtons
              organizationId={settings?.organizationId}
              instanceId={settings?.instanceId}
              openFlyout={props.openFlyout}
              application={props.application}
              featureStatuses={settings.featureStatuses}
              akuityIntelligenceExtension={settings.akuityIntelligenceExtension}
            />
          </QueryClientProvider>
        </Router>
      );
    };
    window.extensionsAPI.registerStatusPanelExtension(
      statusPanelButton,
      'Intelligence',
      'kubevision',
      extensionComponentContainer(
        (props) => (
          <KubeVisionEventTimelineExtension
            {...props}
            organizationId={settings?.organizationId}
            instanceId={settings?.instanceId}
            allowedUsernames={settings.akuityIntelligenceExtension?.allowedUsernames || []}
            allowedGroups={settings.akuityIntelligenceExtension?.allowedGroups || []}
            isk8sFeatureOn={settings.akuityIntelligenceExtension?.enabled}
          />
        ),
        settings
      )
    );
    for (const item of eventTimelineEnabledGroups.filter((item) => item.group !== 'argoproj.io')) {
      window.extensionsAPI.registerResourceExtension(
        extensionComponentContainer(
          (props) => (
            <KubeVisionEventTimelineExtension
              {...props}
              organizationId={settings?.organizationId}
              instanceId={settings?.instanceId}
              allowedUsernames={settings.akuityIntelligenceExtension?.allowedUsernames || []}
              allowedGroups={settings.akuityIntelligenceExtension?.allowedGroups || []}
              isk8sFeatureOn={settings.akuityIntelligenceExtension?.enabled}
            />
          ),
          settings
        ),
        item.group,
        item.kind,
        'Event Timeline',
        {
          icon: 'fa fa-eye'
        }
      );
    }
  }

  if (
    settings.featureStatuses.aiSupportEngineer === FeatureStatus.ENABLED &&
    settings.featureStatuses.multiClusterK8sDashboard === FeatureStatus.ENABLED &&
    settings.akuityIntelligenceExtension?.enabled &&
    settings.akuityIntelligenceExtension?.aiSupportEngineerEnabled
  ) {
    try {
      // Check if element already exists
      const existingDiv = document.querySelector('.ai-support-engineer-extension');
      if (existingDiv) {
        // eslint-disable-next-line no-console
        console.warn('Akuity Intelligence extension element already exists');
        return;
      }

      const aiSupportEngineerDiv = document.createElement('div');
      aiSupportEngineerDiv.classList.add('ai-support-engineer-extension');
      document.body.appendChild(aiSupportEngineerDiv);

      const WrappedAISupportEngineer = extensionComponentContainer(() => {
        return (
          <AISupportEngineerExtension
            isKubeVisionExtensionEnabled={settings.akuityIntelligenceExtension?.enabled}
            organizationId={settings?.organizationId}
            allowedUsernames={settings.akuityIntelligenceExtension?.allowedUsernames || []}
            allowedGroups={settings.akuityIntelligenceExtension?.allowedGroups || []}
            instanceId={settings?.instanceId}
            modelVersion={settings.akuityIntelligenceExtension?.modelVersion}
          />
        );
      }, settings);

      if (useCreateRoot) {
        // React 18
        const ReactDOMClient = ReactDOM as unknown as typeof import('react-dom/client');
        const root = ReactDOMClient.createRoot(aiSupportEngineerDiv);
        root.render(
          <WrappedAISupportEngineer application={{} as Application} resource={{} as Resource} />
        );
        window.__akuityAIRoot = { root, container: aiSupportEngineerDiv };
      } else {
        // React 16.9
        ReactDOM.render(
          <WrappedAISupportEngineer application={{} as Application} resource={{} as Resource} />,
          aiSupportEngineerDiv
        );
        window.__akuityAIRoot = { container: aiSupportEngineerDiv };
      }

      window.extensionsAPI.registerStatusPanelExtension(
        AISupportEngineerListener,
        'Akuity Intelligence',
        'ai_support_engineer'
      );
    } catch (error) {
      // eslint-disable-next-line no-console
      console.error('Failed to initialize Akuity Intelligence extension:', error);
      const div = document.querySelector('.ai-support-engineer-extension');
      if (div) {
        div.remove();
      }
    }
  } else {
    try {
      const existingDiv = document.querySelector('.ai-support-engineer-extension');
      if (existingDiv) {
        if (window.__akuityAIRoot?.root) {
          // React 18
          window.__akuityAIRoot.root.unmount();
        } else {
          // React 16.9
          ReactDOM.unmountComponentAtNode(existingDiv);
        }
        existingDiv.remove();
        delete window.__akuityAIRoot;
      }
    } catch (error) {
      // eslint-disable-next-line no-console
      console.error('Failed to cleanup Akuity Intelligence extension:', error);
    }
  }

  if (settings.applicationSetExtension?.enabled) {
    window.extensionsAPI.registerResourceExtension(
      extensionComponentContainer((props) => <ApplicationSetExtension {...props} />, settings),
      'argoproj.io',
      'ApplicationSet',
      'ApplicationSet',
      {
        icon: 'fa-solid fa-layer-group'
      }
    );

    window.extensionsAPI.registerSystemLevelExtension(
      extensionComponentContainer(() => <ApplicationSetSystemExtension />, settings),
      'ApplicationSets',
      '/applicationsets',
      'fa-solid fa-layer-group'
    );
  }
})(window);
