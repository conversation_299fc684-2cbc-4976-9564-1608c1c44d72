console.log('\n---------------webpack.dev.js----------------\n');

import { Readable } from 'stream';

import CombinedStream from 'combined-stream';
import request from 'sync-request';
import { merge } from 'webpack-merge';

import getDeclarations, { localPortFactory } from '../declarations.js';

import webpackCommon from './webpack.common.js';

const { proxy } = getDeclarations();

/**
 * @type {import('webpack-dev-server').Configuration}
 */
const dev = merge(webpackCommon, {
  mode: 'development',
  module: {
    rules: [
      {
        test: /node_modules\/@xyflow\/react\/.*\.css$/,
        use: ['style-loader', 'css-loader']
      }
    ]
  },
  devServer: {
    historyApiFallback: true,
    port: localPortFactory.kargoExtensionsUi,
    devMiddleware: {
      modifyResponseData: (req, res, data, byteLength) => {
        if (req.path === '/assets/kargo-extensions.js') {
          let settingsData = '{}';
          try {
            settingsData = request(
              'GET',
              `${proxy.extensionsServer.origin}/ext-api/kargo-extensions.js`,
              {
                headers: {
                  accept: 'application/json',
                  'x-forwarded-host': proxy.kargoServer.hostname
                }
              }
            ).getBody('utf8');
          } catch (e) {
            console.error('Failed to fetch extensions settings', e);
          }
          const settings = `window.__akuity = ${settingsData};`;

          const settingsStream = new Readable();
          settingsStream.push(settings);
          settingsStream.push(null);

          const combinedStream = CombinedStream.create();
          combinedStream.append(settingsStream);
          combinedStream.append(data);
          return {
            data: combinedStream,
            byteLength: byteLength + Buffer.byteLength(settings)
          };
        }
        return { data, byteLength };
      }
    },
    // compression broke SSE thus we have to disable it
    // ideally we should disable it only for SSE, keeping this link as reference to try in future - https://composed.blog/sse-webpack-dev-server
    compress: false,
    proxy: [
      {
        context: ['/extension/proxy/ext-api/kargo-extensions.js'],
        target: `http://localhost:${localPortFactory.kargoExtensionsUi}`,
        pathRewrite: {
          '^/extension/proxy/ext-api/kargo-extensions.js': '/assets/kargo-extensions.js'
        },
        secure: false
      },
      {
        context: ['/extension/proxy/ext-api/**'],
        target: proxy.extensionsServer.origin,
        pathRewrite: {
          '^/extension/proxy': ''
        },
        changeOrigin: true,
        secure: false,
        headers: process.env['KARGO_SERVER']
          ? {
              'x-forwarded-host': proxy.kargoServer.hostname
            }
          : undefined
      },
      {
        context: [
          '**',
          '!/extension/proxy/ext-api/kargo-extensions.js',
          '!/extension/proxy/ext-api/**'
        ],
        target: proxy.kargoServer.origin,
        changeOrigin: true,
        secure: false,
        headers: process.env['KARGO_SERVER']
          ? {
              'x-forwarded-host': proxy.kargoServer.hostname
            }
          : undefined
      }
    ]
  },
  output: {
    publicPath: '/'
  },
  watchOptions: {
    ignored: /node_modules/
  }
});

export default dev;
