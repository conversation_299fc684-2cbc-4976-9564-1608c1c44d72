package server

import (
	"context"
	"crypto/rand"
	"database/sql"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"html/template"
	"net/http"
	"os"
	"path"
	"path/filepath"
	"regexp"
	"strings"
	"sync"

	"github.com/auth0/go-auth0/management"
	"github.com/gin-gonic/gin"
	"github.com/go-logr/logr"
	"github.com/go-playground/validator/v10"
	"gopkg.in/gomail.v2"

	"github.com/akuityio/akuity-platform/internal/auth0"
	"github.com/akuityio/akuity-platform/internal/cli"
	"github.com/akuityio/akuity-platform/internal/config"
	"github.com/akuityio/akuity-platform/internal/services/accesscontrol"
	"github.com/akuityio/akuity-platform/internal/services/apikeys"
	"github.com/akuityio/akuity-platform/internal/services/customroles"
	"github.com/akuityio/akuity-platform/internal/services/healthz"
	"github.com/akuityio/akuity-platform/internal/services/teams"
	"github.com/akuityio/akuity-platform/internal/services/users"
	"github.com/akuityio/akuity-platform/internal/services/workspaces"
	ctxutil "github.com/akuityio/akuity-platform/internal/utils/context"
	"github.com/akuityio/akuity-platform/internal/utils/errors"
	httputil "github.com/akuityio/akuity-platform/internal/utils/http"
	"github.com/akuityio/akuity-platform/internal/utils/logging"
	"github.com/akuityio/akuity-platform/internal/utils/mail"
	"github.com/akuityio/akuity-platform/internal/utils/misc"
	akuityvalidator "github.com/akuityio/akuity-platform/internal/utils/validator"
	"github.com/akuityio/akuity-platform/models/audit"
	"github.com/akuityio/akuity-platform/models/client"
	"github.com/akuityio/akuity-platform/models/models"
	"github.com/akuityio/akuity-platform/pkg/billing"
	"github.com/akuityio/akuity-platform/pkg/billing/stripe"
	"github.com/akuityio/akuity-platform/portal/server/middleware"

	_ "embed"
	_ "github.com/jinzhu/gorm/dialects/postgres"
)

const (
	UI_DIR = "./portal/ui/build"
)

//go:embed routing-paths.json
var routingPaths string

type AkuityPortalServer struct {
	Log         *logr.Logger
	Auth0Client auth0.Client
	RepoSet     client.RepoSet
	APIKeySvc   apikeys.Service
	PolicySvc   accesscontrol.PolicyService
	TokenSvc    accesscontrol.TokenService
	UsersSvc    *users.Service
	HealthzSvc  *healthz.Service
	Validator   *validator.Validate

	cfg config.PortalServerConfig
	db  *sql.DB

	auth0APISvc      accesscontrol.Auth0APIService
	billingProviders map[billing.ProviderName]billing.IProvider

	mailer mail.Mailer

	uiFS http.FileSystem

	// gcpMarketPlace error id map
	gcpMarketPlaceErrorIDMap map[string]uint32
	gcpMarketplaceMapMutex   sync.Mutex
}

type ContextAuditActorProvider struct{}

func (c ContextAuditActorProvider) GetActor(ctx context.Context) *models.AuditActor {
	// TODO(soumya) : remove when all gin apis are removed
	// actor for gin apis
	if user := ctxutil.GetUser(ctx); user != nil {
		return &models.AuditActor{ID: user.Email, Type: models.AkuityUserEmailActor}
	}

	// actor for grpc apis
	if actor := ctxutil.GetActor(ctx); actor != nil {
		var clientIP string
		if actor.Extras != nil {
			if ip, ok := actor.Extras["ip"]; ok {
				if ipStr, ok := ip.(string); ok {
					clientIP = ipStr
				}
			}
		}
		switch actor.Type {
		case accesscontrol.ActorTypeUser:
			return &models.AuditActor{ID: actor.Extras["email"].(string), Type: models.AkuityUserEmailActor, IP: clientIP}
		case accesscontrol.ActorTypeAPIKey:
			return &models.AuditActor{ID: actor.ID, Type: models.AkuityTokenActor, IP: clientIP}
		}
	}

	return &models.AuditActor{Type: models.UnknownActor}
}

func NewAkuityPortalServer(cfg config.PortalServerConfig, log *logr.Logger, tokenService accesscontrol.TokenService, auth0Manager *management.Management, sqldb *sql.DB) *AkuityPortalServer {
	repoSet := client.NewRepoSet(sqldb)

	v := akuityvalidator.New()
	teamSvc := teams.NewService(sqldb)
	workspaceSvc := workspaces.NewService(sqldb, teamSvc, cfg.FeatureGatesSource)
	aks := apikeys.NewService(repoSet, v)
	acps := accesscontrol.NewPolicyService(v, customroles.New(repoSet), workspaceSvc, aks, teamSvc)
	auth0APISvc, err := accesscontrol.NewAuth0APIService(cfg.Auth0.Portal())
	cli.CheckErr(err)

	us := users.NewService(repoSet, cfg.UsageLimits, log)

	argoCDVersionsList, err := misc.GetArgoCDVersions(*log)
	if err != nil {
		cli.CheckErr(fmt.Errorf("failed to get valid argo versions: %w", err))
	}

	kargoVersionList, unstableKargoVersion, err := misc.GetKargoVersions(*log)
	if err != nil {
		cli.CheckErr(fmt.Errorf("failed to get valid kargo versions: %w", err))
	}

	audit.AddAuditHooks(log, &ContextAuditActorProvider{}, argoCDVersionsList, kargoVersionList, unstableKargoVersion)

	var billingProviders map[billing.ProviderName]billing.IProvider
	stripeCfg, err := config.NewStripeConfig()
	cli.CheckErr(err)

	if stripeCfg.Key != "" {
		billingProviders = map[billing.ProviderName]billing.IProvider{
			billing.StripeBillingProvider: stripe.NewStripeProvider(stripeCfg.Key, repoSet),
		}
	}

	logServicesEnabled(cfg, stripeCfg, log)

	var mailer mail.Mailer = &mail.NoOpMailer{}

	if cfg.SMTP.Host != "" {
		mailer = gomail.NewDialer(cfg.SMTP.Host, cfg.SMTP.Port, cfg.SMTP.User, cfg.SMTP.Password)
	}

	return &AkuityPortalServer{
		Log:                      log,
		RepoSet:                  repoSet,
		PolicySvc:                acps,
		TokenSvc:                 tokenService,
		APIKeySvc:                aks,
		UsersSvc:                 us,
		HealthzSvc:               healthz.NewService(cfg, repoSet, tokenService, billingProviders),
		Validator:                v,
		cfg:                      cfg,
		db:                       sqldb,
		auth0APISvc:              auth0APISvc,
		billingProviders:         billingProviders,
		mailer:                   mailer,
		uiFS:                     http.Dir(UI_DIR),
		gcpMarketPlaceErrorIDMap: map[string]uint32{},
	}
}

func logServicesEnabled(portalConfig config.PortalServerConfig, stripeConfig config.StripeConfig, log *logr.Logger) {
	maxMindConfig := portalConfig.MaxMind
	log.Info(fmt.Sprintf("FeaturesSource : %q", portalConfig.FeatureGatesSource))
	log.Info(fmt.Sprintf("SMTP    enabled: %t", portalConfig.SMTP.Host != ""))
	log.Info(fmt.Sprintf("Stripe  enabled: %t", stripeConfig.Key != ""))
	log.Info(fmt.Sprintf("MaxMind enabled: %t", maxMindConfig.AccountID != ""))
	log.Info(fmt.Sprintf("Sentry enabled : %t", os.Getenv("PLATFORM_SENTRY_DSN") != ""))
	log.Info(fmt.Sprintf("Slack callbacks enabled : %t", portalConfig.SlackCallbackIncomingWebhook != ""))
}

func (s *AkuityPortalServer) NewAPIRequestHandler() http.HandlerFunc {
	r := gin.New()
	r.ContextWithFallback = true
	r.Use(gin.RecoveryWithWriter(logging.SentryWriter{Writer: gin.DefaultErrorWriter}))
	r.Use(httputil.NewLogger(*s.Log, httputil.NewMetricCollector(), httputil.WithSkipPaths("/api/healthz")))
	r.Use(httputil.InjectVersionHeader())
	r.Use(middleware.InjectPatchFuncPlaceholder())

	api := r.Group("/api")
	protected := api.Group("")
	exposed := api.Group("")

	s.RouteAuth(exposed.Group("/auth"), s.cfg.Dev.Auth)

	protected.Use(middleware.NewAuthHandler(s.RepoSet.Organizations(), s.PolicySvc, s.TokenSvc, s.APIKeySvc, s.UsersSvc, s.cfg, s.Log))

	s.RouteInstances(protected.Group("/instances"))
	s.RouteWebhook(api)
	if !config.IsSelfHosted {
		s.RouteAuth0(exposed.Group("/auth0"))
		s.RouteStripeBilling(exposed.Group("/billing"))
		s.RouteAWSMarketPlace(exposed.Group("/aws"))
		s.RouteGCPMarketPlace(exposed.Group("/gcp"))
		s.RouteAzureMarketPlace(exposed.Group("/azure"))
		exposed.POST("/mfa", middleware.RestrictAccess(), s.mfa)
	} else {
		s.RouteLicense(exposed.Group("/license"))
	}

	exposed.GET("/healthz", s.healthCheck)
	return func(w http.ResponseWriter, req *http.Request) {
		httputil.SetNoCacheHeaders(w, true)
		r.ServeHTTP(w, req)
	}
}

func (s *AkuityPortalServer) healthCheck(c *gin.Context) {
	var err error
	response := "Ok"

	if c.Writer != nil {
		httputil.SetNoCacheHeaders(c.Writer, false)
	}

	switch c.Query("deepcheck") {
	case "db":
		response, err = s.HealthzSvc.Retry(c, healthz.NumberOfRetries, healthz.RetryInterval, s.HealthzSvc.CheckDBHealth)
	case "auth0":
		response, err = s.HealthzSvc.Retry(c, healthz.NumberOfRetries, healthz.RetryInterval, s.HealthzSvc.CheckAuth0Health)
	case "stripe":
		response, err = s.HealthzSvc.Retry(c, healthz.NumberOfRetries, healthz.RetryInterval, s.HealthzSvc.CheckStripeHealth)
	case "all":
		response, err = s.HealthzSvc.CheckAllHealth(c)
	}

	if err == nil {
		c.String(http.StatusOK, response)
		return
	}

	s.Log.Error(err, "Healthcheck(s) failed")
	c.String(http.StatusInternalServerError, response)
}

func loadValidRoutes() (map[string]*regexp.Regexp, error) {
	var routes map[string]string
	if err := json.Unmarshal([]byte(routingPaths), &routes); err != nil {
		return nil, err
	}

	routeMap := make(map[string]*regexp.Regexp)
	for _, route := range routes {
		routeMap[route] = convertPatternToRegex(route)
	}

	return routeMap, nil
}

func convertPatternToRegex(pattern string) *regexp.Regexp {
	// Escape special characters in the pattern except for placeholders
	pattern = regexp.QuoteMeta(pattern)
	pattern = strings.ReplaceAll(pattern, "\\:", ":")
	// Replace :param with (?P<param>[^/]+)
	pattern = regexp.MustCompile(`:([^/]+)`).ReplaceAllString(pattern, `(?P<$1>[^/]+)`)
	// Replace * with (.+)
	pattern = strings.ReplaceAll(pattern, "\\*", "(.+)")

	return regexp.MustCompile("^" + pattern + "$")
}

func isValidRoute(inputRoute string, routesMap map[string]*regexp.Regexp) bool {
	for _, regexStr := range routesMap {
		if regexStr.MatchString(inputRoute) {
			return true
		}
	}
	return false
}

func generateCSPNonce() (string, error) {
	bytes := make([]byte, 16)
	if _, err := rand.Read(bytes); err != nil {
		return "", err
	}
	return base64.StdEncoding.EncodeToString(bytes), nil
}

func (s *AkuityPortalServer) NewDashboardRequestHandler(uiDir string) http.HandlerFunc {
	fs := http.FileServer(http.Dir(uiDir))
	uiDirPrefix := strings.TrimSuffix(uiDir, path.Clean(uiDir))
	validRoutes, err := loadValidRoutes()
	if err != nil {
		s.Log.Error(err, "failed to load valid routes")
	}

	serveIndexHTML := func(w http.ResponseWriter, req *http.Request) {
		nonce, err := generateCSPNonce()
		if err != nil {
			s.Log.Error(err, "failed to generate CSP nonce")
			http.Error(w, "Internal Server Error", http.StatusInternalServerError)
			return
		}

		tmpl, err := template.ParseFiles(filepath.Join(uiDir, "index.html"))
		if err != nil {
			s.Log.Error(err, "failed to parse index.html template")
			http.Error(w, "Internal Server Error", http.StatusInternalServerError)
			return
		}

		httputil.SetNoCacheHeaders(w, false)
		w.Header().Set("Content-Type", "text/html; charset=utf-8")
		w.Header().Set("Content-Security-Policy", buildContentSecurityPolicy(defaultCSP, nonce))

		data := map[string]string{
			"cspNonce": nonce,
		}
		if err := tmpl.Execute(w, data); err != nil {
			s.Log.Error(err, "failed to execute index.html template")
			http.Error(w, "Internal Server Error", http.StatusInternalServerError)
			return
		}
	}

	return func(w http.ResponseWriter, req *http.Request) {
		// see https://github.com/akuityio/akuity-platform/issues/1965
		if w == nil {
			return
		}
		reqPath := uiDirPrefix + filepath.Join(uiDir, req.URL.Path)
		if !strings.HasPrefix(reqPath, uiDir) {
			http.Error(w, "Forbidden", http.StatusForbidden)
			return
		}

		info, err := os.Stat(reqPath)
		if err != nil && !os.IsNotExist(err) {
			s.Log.Error(err, "stat request path")
			http.Error(w, "Internal Server Error", http.StatusInternalServerError)
		} else if os.IsNotExist(err) || err == nil && info.IsDir() {
			if isValidRoute(req.URL.Path, validRoutes) {
				serveIndexHTML(w, req)
			} else {
				w.Header().Set("Content-Type", "application/json")
				w.WriteHeader(http.StatusNotFound)
				if _, err := w.Write([]byte(`{"error": "Not Found"}`)); err != nil {
					s.Log.Error(err, "failed to write response")
				}
				return
			}
		} else {
			// Check if the requested file is index.html and handle templating
			if filepath.Base(reqPath) == "index.html" {
				serveIndexHTML(w, req)
			} else {
				fs.ServeHTTP(w, req)
			}
		}
	}
}

func (s *AkuityPortalServer) Handler(f func(c *gin.Context) (interface{}, error)) func(c *gin.Context) {
	return func(c *gin.Context) {
		data, err := f(c)
		if err != nil {
			s.abortWithError(c, err, "failed to process request")
			return
		}
		if data == nil {
			c.Status(c.Writer.Status())
			return
		}
		c.JSON(c.Writer.Status(), data)
	}
}

func (s *AkuityPortalServer) abortWithError(c *gin.Context, err error, message string) {
	s.Log.Error(err, message)
	_ = c.Error(err)
	apiErr := errors.GetAPIStatus(err)
	if apiErr.Status == http.StatusInternalServerError {
		apiErr.Message = "An internal error has occurred"
	}
	c.JSON(apiErr.Status, apiErr)
}
