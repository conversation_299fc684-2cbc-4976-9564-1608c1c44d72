package server

import (
	"fmt"
	"strings"
)

// CSPConfig represents the allow-lists for each Content Security Policy directive.
// Nonces for inline script/style will be appended at render time.
type CSPConfig struct {
	DefaultSrc []string
	FrameSrc   []string
	ScriptSrc  []string
	ImgSrc     []string
	FontSrc    []string
	StyleSrc   []string
	ConnectSrc []string
	ObjectSrc  []string
}

// defaultCSP contains the centralized per-directive sources. Update these lists
// to modify the CSP without touching header-building logic.
var defaultCSP = CSPConfig{
	DefaultSrc: []string{"'self'"},
	FrameSrc:   []string{"'self'", "https://app.supademo.com"},
	ScriptSrc: []string{
		"'self'", "https://app.supademo.com",
		"https://*.googletagmanager.com", "https://*.sentry.io",
	},
	ImgSrc: []string{
		"'self'", "data:", "https://argo-cd.readthedocs.io",
		"https://artifacthub.io", "https://*.google-analytics.com",
		"https://*.googletagmanager.com",
	},
	FontSrc:  []string{"'self'"},
	StyleSrc: []string{"'self'"},
	ConnectSrc: []string{
		"https://dl.akuity.io", "https://artifacthub.io",
		"https://*.google-analytics.com", "https://*.analytics.google.com",
		"https://*.googletagmanager.com", "https://*.ingest.sentry.io",
		"https://api.github.com", "https://raw.githubusercontent.com", "'self'",
	},
	ObjectSrc: []string{"'none'"},
}

// buildContentSecurityPolicy generates the CSP header from the config and nonce.
func buildContentSecurityPolicy(cfg CSPConfig, nonce string) string {
	var directives []string

	add := func(name string, values []string, includeNonce bool) {
		if len(values) == 0 && !includeNonce {
			return
		}
		srcs := make([]string, 0, len(values)+1)
		srcs = append(srcs, values...)
		if includeNonce && nonce != "" {
			srcs = append(srcs, fmt.Sprintf("'nonce-%s'", nonce))
		}
		directives = append(directives, fmt.Sprintf("%s %s", name, strings.Join(srcs, " ")))
	}

	add("default-src", cfg.DefaultSrc, false)
	add("frame-src", cfg.FrameSrc, false)
	add("script-src", cfg.ScriptSrc, true)
	add("img-src", cfg.ImgSrc, false)
	add("font-src", cfg.FontSrc, false)
	add("style-src", cfg.StyleSrc, true)
	add("connect-src", cfg.ConnectSrc, false)
	add("object-src", cfg.ObjectSrc, false)

	return strings.Join(directives, "; ")
}
