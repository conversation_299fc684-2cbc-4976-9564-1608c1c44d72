package server

import (
	"fmt"
	"testing"
)

func TestBuildContentSecurityPolicy(t *testing.T) {
	nonce := "abc123"
	cfg := CSPConfig{
		DefaultSrc: []string{"'self'", "https://example.com"},
		FrameSrc:   []string{"https://frames.example.com"},
		ScriptSrc:  []string{}, // should include only nonce
		ImgSrc:     []string{"data:"},
		FontSrc:    []string{}, // omitted entirely
		StyleSrc:   []string{}, // should include only nonce
		ConnectSrc: []string{"'self'", "https://api.example.com"},
		ObjectSrc:  []string{"'none'"},
	}

	got := buildContentSecurityPolicy(cfg, nonce)
	want := fmt.Sprintf("default-src 'self' https://example.com; frame-src https://frames.example.com; script-src 'nonce-%v'; img-src data:; style-src 'nonce-%v'; connect-src 'self' https://api.example.com; object-src 'none'", nonce, nonce)
	if got != want {
		t.Fatalf("CSP mismatch (custom cfg):\nwant: %q\n got: %q", want, got)
	}
}
